#!/usr/bin/env node

/**
 * Performance Testing Script
 * 
 * This script runs comprehensive performance tests including:
 * - Bundle size analysis
 * - Lighthouse audits
 * - Load testing
 * - Performance regression detection
 */

const { execSync, spawn } = require('child_process');
const fs = require('fs');
const path = require('path');

// Configuration
const CONFIG = {
  urls: [
    'http://localhost:3000',
    'http://localhost:3000/dashboard',
    'http://localhost:3000/projects',
    'http://localhost:3000/contractors',
  ],
  lighthouse: {
    outputDir: './performance-reports',
    categories: ['performance', 'accessibility', 'best-practices', 'seo'],
    thresholds: {
      performance: 90,
      accessibility: 95,
      'best-practices': 90,
      seo: 90,
    },
  },
  bundleSize: {
    maxSizeKB: 500, // Maximum initial bundle size in KB
    maxChunks: 20,  // Maximum number of chunks
  },
};

class PerformanceTester {
  constructor() {
    this.results = {
      lighthouse: {},
      bundleSize: {},
      loadTesting: {},
      timestamp: new Date().toISOString(),
    };
  }

  async run() {
    console.log('🚀 Starting Performance Testing Suite...\n');

    try {
      // Ensure output directory exists
      this.ensureOutputDir();

      // Run bundle analysis
      await this.runBundleAnalysis();

      // Start development server for testing
      const server = await this.startDevServer();

      // Wait for server to be ready
      await this.waitForServer();

      // Run Lighthouse audits
      await this.runLighthouseAudits();

      // Run load testing
      await this.runLoadTesting();

      // Stop server
      server.kill();

      // Generate report
      await this.generateReport();

      console.log('✅ Performance testing completed successfully!');
      console.log(`📊 Report generated: ${path.join(CONFIG.lighthouse.outputDir, 'performance-summary.json')}`);

    } catch (error) {
      console.error('❌ Performance testing failed:', error.message);
      process.exit(1);
    }
  }

  ensureOutputDir() {
    if (!fs.existsSync(CONFIG.lighthouse.outputDir)) {
      fs.mkdirSync(CONFIG.lighthouse.outputDir, { recursive: true });
    }
  }

  async runBundleAnalysis() {
    console.log('📦 Running bundle analysis...');
    
    try {
      // Build with bundle analyzer
      execSync('ANALYZE=true pnpm build', { 
        stdio: 'inherit',
        env: { ...process.env, ANALYZE: 'true' }
      });

      // Parse build output for bundle sizes
      const buildDir = './.next';
      if (fs.existsSync(buildDir)) {
        const stats = this.analyzeBundleStats(buildDir);
        this.results.bundleSize = stats;
        
        console.log(`📊 Bundle Analysis Results:`);
        console.log(`   Initial Bundle: ${stats.initialSize}KB`);
        console.log(`   Total Chunks: ${stats.chunkCount}`);
        console.log(`   Largest Chunk: ${stats.largestChunk}KB`);
        
        // Check thresholds
        if (stats.initialSize > CONFIG.bundleSize.maxSizeKB) {
          console.warn(`⚠️  Initial bundle size (${stats.initialSize}KB) exceeds threshold (${CONFIG.bundleSize.maxSizeKB}KB)`);
        }
        
        if (stats.chunkCount > CONFIG.bundleSize.maxChunks) {
          console.warn(`⚠️  Chunk count (${stats.chunkCount}) exceeds threshold (${CONFIG.bundleSize.maxChunks})`);
        }
      }
      
      console.log('✅ Bundle analysis completed\n');
    } catch (error) {
      console.error('❌ Bundle analysis failed:', error.message);
      throw error;
    }
  }

  analyzeBundleStats(buildDir) {
    // This is a simplified analysis - in a real scenario, you'd parse the actual webpack stats
    const staticDir = path.join(buildDir, 'static');
    let totalSize = 0;
    let chunkCount = 0;
    let largestChunk = 0;

    if (fs.existsSync(staticDir)) {
      const files = this.getAllFiles(staticDir);
      
      files.forEach(file => {
        if (file.endsWith('.js') || file.endsWith('.css')) {
          const stats = fs.statSync(file);
          const sizeKB = Math.round(stats.size / 1024);
          totalSize += sizeKB;
          chunkCount++;
          largestChunk = Math.max(largestChunk, sizeKB);
        }
      });
    }

    return {
      initialSize: Math.round(totalSize * 0.3), // Estimate initial bundle as 30% of total
      totalSize,
      chunkCount,
      largestChunk,
    };
  }

  getAllFiles(dir) {
    let files = [];
    const items = fs.readdirSync(dir);
    
    items.forEach(item => {
      const fullPath = path.join(dir, item);
      if (fs.statSync(fullPath).isDirectory()) {
        files = files.concat(this.getAllFiles(fullPath));
      } else {
        files.push(fullPath);
      }
    });
    
    return files;
  }

  async startDevServer() {
    console.log('🖥️  Starting development server...');
    
    const server = spawn('pnpm', ['dev'], {
      stdio: 'pipe',
      detached: false,
    });

    server.stdout.on('data', (data) => {
      const output = data.toString();
      if (output.includes('Ready')) {
        console.log('✅ Development server started');
      }
    });

    server.stderr.on('data', (data) => {
      console.error('Server error:', data.toString());
    });

    return server;
  }

  async waitForServer(timeout = 30000) {
    console.log('⏳ Waiting for server to be ready...');
    
    const startTime = Date.now();
    
    while (Date.now() - startTime < timeout) {
      try {
        const response = await fetch('http://localhost:3000');
        if (response.ok) {
          console.log('✅ Server is ready\n');
          return;
        }
      } catch (error) {
        // Server not ready yet
      }
      
      await new Promise(resolve => setTimeout(resolve, 1000));
    }
    
    throw new Error('Server failed to start within timeout');
  }

  async runLighthouseAudits() {
    console.log('🔍 Running Lighthouse audits...');
    
    for (const url of CONFIG.urls) {
      console.log(`   Auditing: ${url}`);
      
      try {
        const outputPath = path.join(
          CONFIG.lighthouse.outputDir,
          `lighthouse-${url.replace(/[^a-zA-Z0-9]/g, '-')}.json`
        );
        
        execSync(`lighthouse ${url} --output=json --output-path=${outputPath} --chrome-flags="--headless" --quiet`, {
          stdio: 'pipe'
        });
        
        // Parse results
        const results = JSON.parse(fs.readFileSync(outputPath, 'utf8'));
        const scores = {};
        
        CONFIG.lighthouse.categories.forEach(category => {
          const score = Math.round(results.lhr.categories[category].score * 100);
          scores[category] = score;
          
          if (score < CONFIG.lighthouse.thresholds[category]) {
            console.warn(`⚠️  ${category} score (${score}) below threshold (${CONFIG.lighthouse.thresholds[category]})`);
          }
        });
        
        this.results.lighthouse[url] = scores;
        console.log(`   Scores: ${Object.entries(scores).map(([k, v]) => `${k}:${v}`).join(', ')}`);
        
      } catch (error) {
        console.error(`❌ Lighthouse audit failed for ${url}:`, error.message);
        this.results.lighthouse[url] = { error: error.message };
      }
    }
    
    console.log('✅ Lighthouse audits completed\n');
  }

  async runLoadTesting() {
    console.log('⚡ Running basic load testing...');
    
    const testUrl = CONFIG.urls[0];
    const concurrentRequests = 10;
    const totalRequests = 100;
    
    try {
      const startTime = Date.now();
      const promises = [];
      
      for (let i = 0; i < totalRequests; i++) {
        if (i % concurrentRequests === 0 && i > 0) {
          await Promise.all(promises);
          promises.length = 0;
        }
        
        promises.push(
          fetch(testUrl)
            .then(response => ({
              status: response.status,
              time: Date.now() - startTime,
            }))
            .catch(error => ({
              error: error.message,
              time: Date.now() - startTime,
            }))
        );
      }
      
      const results = await Promise.all(promises);
      const endTime = Date.now();
      
      const successfulRequests = results.filter(r => r.status === 200).length;
      const averageResponseTime = results.reduce((sum, r) => sum + (r.time || 0), 0) / results.length;
      
      this.results.loadTesting = {
        totalRequests,
        successfulRequests,
        failedRequests: totalRequests - successfulRequests,
        averageResponseTime: Math.round(averageResponseTime),
        totalTime: endTime - startTime,
        requestsPerSecond: Math.round(totalRequests / ((endTime - startTime) / 1000)),
      };
      
      console.log(`   Successful requests: ${successfulRequests}/${totalRequests}`);
      console.log(`   Average response time: ${Math.round(averageResponseTime)}ms`);
      console.log(`   Requests per second: ${this.results.loadTesting.requestsPerSecond}`);
      
    } catch (error) {
      console.error('❌ Load testing failed:', error.message);
      this.results.loadTesting = { error: error.message };
    }
    
    console.log('✅ Load testing completed\n');
  }

  async generateReport() {
    console.log('📊 Generating performance report...');
    
    const reportPath = path.join(CONFIG.lighthouse.outputDir, 'performance-summary.json');
    fs.writeFileSync(reportPath, JSON.stringify(this.results, null, 2));
    
    // Generate HTML report
    const htmlReport = this.generateHTMLReport();
    const htmlPath = path.join(CONFIG.lighthouse.outputDir, 'performance-report.html');
    fs.writeFileSync(htmlPath, htmlReport);
    
    console.log('✅ Performance report generated');
  }

  generateHTMLReport() {
    return `
<!DOCTYPE html>
<html>
<head>
    <title>Performance Test Report</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 40px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 8px; }
        .section { margin: 20px 0; }
        .metric { display: inline-block; margin: 10px; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        .good { background: #d4edda; }
        .warning { background: #fff3cd; }
        .error { background: #f8d7da; }
    </style>
</head>
<body>
    <div class="header">
        <h1>Performance Test Report</h1>
        <p>Generated: ${this.results.timestamp}</p>
    </div>
    
    <div class="section">
        <h2>Bundle Analysis</h2>
        <div class="metric">Initial Bundle: ${this.results.bundleSize.initialSize || 'N/A'}KB</div>
        <div class="metric">Total Chunks: ${this.results.bundleSize.chunkCount || 'N/A'}</div>
        <div class="metric">Largest Chunk: ${this.results.bundleSize.largestChunk || 'N/A'}KB</div>
    </div>
    
    <div class="section">
        <h2>Lighthouse Scores</h2>
        ${Object.entries(this.results.lighthouse).map(([url, scores]) => `
            <h3>${url}</h3>
            ${typeof scores === 'object' && !scores.error ? 
                Object.entries(scores).map(([category, score]) => 
                    `<div class="metric ${score >= 90 ? 'good' : score >= 70 ? 'warning' : 'error'}">${category}: ${score}</div>`
                ).join('') : 
                `<div class="metric error">Error: ${scores.error || 'Unknown error'}</div>`
            }
        `).join('')}
    </div>
    
    <div class="section">
        <h2>Load Testing</h2>
        <div class="metric">Successful Requests: ${this.results.loadTesting.successfulRequests || 'N/A'}/${this.results.loadTesting.totalRequests || 'N/A'}</div>
        <div class="metric">Average Response Time: ${this.results.loadTesting.averageResponseTime || 'N/A'}ms</div>
        <div class="metric">Requests/Second: ${this.results.loadTesting.requestsPerSecond || 'N/A'}</div>
    </div>
</body>
</html>
    `;
  }
}

// Run the performance tester
if (require.main === module) {
  const tester = new PerformanceTester();
  tester.run().catch(console.error);
}

module.exports = PerformanceTester;
