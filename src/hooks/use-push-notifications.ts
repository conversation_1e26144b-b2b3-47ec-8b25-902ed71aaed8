"use client";

import { useMutation, useQuery } from "@tanstack/react-query";
import { useCallback, useEffect, useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { notifySuccess } from "@/lib/notifications";
import {
  sendPushNotification,
  subscribeToPushNotifications,
  unsubscribeFromPushNotifications,
} from "@/lib/notifications-push";

export function usePushNotifications() {
  const [isSupported, setIsSupported] = useState(false);
  const [permission, setPermission] =
    useState<NotificationPermission>("default");
  const [isSubscribed, setIsSubscribed] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const trpc = useTRPC();

  // Get user settings to check if push notifications are enabled
  const { data: userSettings } = useQuery(
    trpc.users.getSettings.queryOptions(),
  );

  const removePushSubscription = useMutation(
    trpc.users.removePushSubscription.mutationOptions(),
  );

  useEffect(() => {
    // Check if browser supports notifications
    const checkSupport = async () => {
      const supported =
        "Notification" in window && "serviceWorker" in navigator;
      setIsSupported(supported);

      if (supported) {
        setPermission(Notification.permission);

        // Check if already subscribed
        if (Notification.permission === "granted") {
          const registration = await navigator.serviceWorker.ready;
          const subscription = await registration.pushManager.getSubscription();
          setIsSubscribed(!!subscription);
        }
      }
    };

    checkSupport();
  }, []);

  // Subscribe to push notifications
  const subscribe = useCallback(async () => {
    if (!isSupported || permission === "denied" || isSubscribed) {
      return false;
    }

    setIsLoading(true);
    try {
      // Use the utility function from notifications-push.ts
      const success = await subscribeToPushNotifications();

      if (success) {
        setIsSubscribed(true);
        setPermission("granted");
        notifySuccess(
          "Notifications enabled",
          "You will now receive push notifications",
        );

        // No need to manually save subscription as it's handled in subscribeToPushNotifications
      }

      setIsLoading(false);
      return success;
    } catch (error) {
      setIsLoading(false);
      toast.error("Failed to enable notifications", {
        description: error instanceof Error ? error.message : "Unknown error",
      });
      return false;
    }
  }, [isSupported, permission, isSubscribed]);

  // Unsubscribe from push notifications
  const unsubscribe = useCallback(async () => {
    if (!isSupported || !isSubscribed) {
      return false;
    }

    setIsLoading(true);
    try {
      // Use the utility function from notifications-push.ts
      const success = await unsubscribeFromPushNotifications();

      if (success) {
        setIsSubscribed(false);
        toast.success("Notifications disabled");

        // Remove the subscription from your server
        await removePushSubscription.mutateAsync();
      }

      setIsLoading(false);
      return success;
    } catch {
      setIsLoading(false);
      toast.error("Failed to disable notifications");
      return false;
    }
  }, [isSupported, isSubscribed, removePushSubscription]);

  // Send a notification
  const sendNotification = useCallback(
    async (
      title: string,
      body: string,
      options?: {
        accountId?: string;
        data?: Record<string, unknown>;
        icon?: string;
        badge?: string;
        tag?: string;
        actions?: Array<{
          action: string;
          title: string;
          icon?: string;
        }>;
      },
    ) => {
      if (!isSupported || !isSubscribed) return false;

      try {
        return await sendPushNotification(title, body, options);
      } catch {
        toast.error("Failed to send notification");
        return false;
      }
    },
    [isSupported, isSubscribed],
  );

  return {
    isSupported,
    permission,
    isSubscribed,
    isLoading,
    subscribe,
    unsubscribe,
    sendNotification,
    pushEnabled: userSettings?.notifications?.push?.enabled || false,
  };
}
