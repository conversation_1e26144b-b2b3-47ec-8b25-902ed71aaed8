"use client";

import { useOrganization } from "@/components/contexts/organization-context";
import { useSession } from "@/lib/auth-client";

/**
 * Hook that ensures an organization is available for contractors
 * Throws an error if used by homeowners or if no organization exists
 */
export function useOrganizationRequired() {
  const { data: session } = useSession();
  const { organization, isLoading, error } = useOrganization();

  if (session?.user?.role === "homeowner") {
    throw new Error("Organization context is not available for homeowners");
  }

  if (error) {
    throw error;
  }

  if (!isLoading && !organization && session?.user?.role === "contractor") {
    throw new Error("No organization found for contractor. Please create an organization first.");
  }

  return {
    organization: organization!,
    isLoading,
    error,
  };
}

/**
 * Hook that safely gets organization data, returning null for homeowners
 */
export function useOrganizationSafe() {
  const { data: session } = useSession();
  const { organization, isLoading, error } = useOrganization();

  if (session?.user?.role === "homeowner") {
    return {
      organization: null,
      isLoading: false,
      error: null,
    };
  }

  return {
    organization,
    isLoading,
    error,
  };
}
