import { createId } from "@paralleldrive/cuid2";
import type { UIMessage } from "ai";
import { type InferEnum, type InferSelectModel, relations } from "drizzle-orm";
import {
  boolean,
  doublePrecision,
  foreignKey,
  geometry,
  index,
  integer,
  jsonb,
  pgEnum,
  pgTable,
  primaryKey,
  text,
  timestamp,
  uniqueIndex,
  vector,
} from "drizzle-orm/pg-core";
import type { UserSettings } from "@/types/user-settings";

export const accountRole = pgEnum("account_role", [
  "HOMEOWNER",
  "PROFESSIONAL",
  "ADMIN",
]);
export const bidStatus = pgEnum("bid_status", [
  "PROPOSED",
  "ACCEPTED",
  "REJECTED",
  "CANCELED",
  "WITHDRAWN",
]);
export const jobStatus = pgEnum("job_status", [
  "DRAFT",
  "PUBLISHED",
  "CLOSED",
  "CANCELED",
  "AWARDED",
  "COMPLETED",
]);
export const jobType = pgEnum("job_type", ["STANDARD", "QUICK_HIRE"]);
export const scheduleStatus = pgEnum("schedule_status", [
  "PROPOSED",
  "CONFIRMED",
  "RESCHEDULED",
  "CANCELED",
]);

export const user = pgTable("user", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => createId()),
  name: text("name").notNull(),
  email: text("email").notNull().unique(),
  emailVerified: boolean("email_verified")
    .$defaultFn(() => false)
    .notNull(),
  image: text("image"),
  createdAt: timestamp("created_at").defaultNow().notNull(),
  updatedAt: timestamp("updated_at").defaultNow().notNull(),
  role: text("role"),
  banned: boolean("banned"),
  banReason: text("ban_reason"),
  banExpires: timestamp("ban_expires"),
  twoFactorEnabled: boolean("two_factor_enabled"),
  onboardingComplete: boolean("onboarding_complete"),
  settings: jsonb("settings")
    .$defaultFn(() => ({}))
    .$type<UserSettings>(),
});

export const session = pgTable("session", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => createId()),
  expiresAt: timestamp("expires_at").notNull(),
  token: text("token").notNull().unique(),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
  ipAddress: text("ip_address"),
  userAgent: text("user_agent"),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  impersonatedBy: text("impersonated_by"),
});

export const account = pgTable("account", {
  id: text("id")
    .primaryKey()
    .$defaultFn(() => createId()),
  accountId: text("account_id").notNull(),
  providerId: text("provider_id").notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
  accessToken: text("access_token"),
  refreshToken: text("refresh_token"),
  idToken: text("id_token"),
  accessTokenExpiresAt: timestamp("access_token_expires_at"),
  refreshTokenExpiresAt: timestamp("refresh_token_expires_at"),
  scope: text("scope"),
  password: text("password"),
  createdAt: timestamp("created_at").notNull(),
  updatedAt: timestamp("updated_at").notNull(),
});

export const verification = pgTable("verification", {
  id: text("id").primaryKey(),
  identifier: text("identifier").notNull(),
  value: text("value").notNull(),
  expiresAt: timestamp("expires_at").notNull(),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at").defaultNow(),
});

export const twoFactor = pgTable("two_factor", {
  id: text("id").primaryKey(),
  secret: text("secret").notNull(),
  backupCodes: text("backup_codes").notNull(),
  userId: text("user_id")
    .notNull()
    .references(() => user.id, { onDelete: "cascade" }),
});

export const task = pgTable(
  "task",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    jobId: text().notNull(),
    tradeId: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.jobId],
      foreignColumns: [job.id],
      name: "task_jobId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.tradeId],
      foreignColumns: [trade.id],
      name: "task_tradeId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const trade = pgTable(
  "trade",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
    availableForQuickHire: boolean().default(false).notNull(),
  },
  (table) => [
    uniqueIndex("trade_name_key").using(
      "btree",
      table.name.asc().nullsLast().op("text_ops"),
    ),
  ],
);

export const organization = pgTable(
  "organization",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    tradeId: text(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
    description: text(),
    logoUrl: text(),
    email: text(),
    phone: text(),
    addressId: text().notNull(),
    acceptsQuickHire: boolean().default(false).notNull(),
  },
  (table) => [
    uniqueIndex("organization_addressId_key").using(
      "btree",
      table.addressId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.tradeId],
      foreignColumns: [trade.id],
      name: "organization_tradeId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.addressId],
      foreignColumns: [address.id],
      name: "organization_addressId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const chat = pgTable(
  "chat",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    bidId: text(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
    jobId: text(),
  },
  (table) => [
    uniqueIndex("chat_bidId_key").using(
      "btree",
      table.bidId.asc().nullsLast().op("text_ops"),
    ),
    uniqueIndex("chat_jobId_key").using(
      "btree",
      table.jobId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.bidId],
      foreignColumns: [bid.id],
      name: "chat_bidId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.jobId],
      foreignColumns: [job.id],
      name: "chat_jobId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const message = pgTable(
  "message",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    content: text().notNull(),
    senderId: text().notNull(),
    senderType: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
    chatId: text().notNull(),
    senderAvatarUrl: text(),
    senderInitials: text(),
    commandData: text(),
    isCommand: boolean().default(false).notNull(),
  },
  (table) => [
    index("message_chatId_idx").using(
      "btree",
      table.chatId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.chatId],
      foreignColumns: [chat.id],
      name: "message_chatId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const jobImage = pgTable(
  "job_image",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    url: text().notNull(),
    description: text(),
    jobId: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
  },
  (table) => [
    foreignKey({
      columns: [table.jobId],
      foreignColumns: [job.id],
      name: "job_image_jobId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const bid = pgTable(
  "bid",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    jobId: text().notNull(),
    organizationId: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
    status: bidStatus().default("PROPOSED").notNull(),
    amount: integer().default(0).notNull(),
    description: text().default("").notNull(),
    estimatedDuration: integer().default(7).notNull(),
  },
  (table) => [
    foreignKey({
      columns: [table.jobId],
      foreignColumns: [job.id],
      name: "bid_jobId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.organizationId],
      foreignColumns: [organization.id],
      name: "bid_organizationId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const property = pgTable(
  "property",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
    imageUrl: text().notNull(),
    addressId: text().notNull(),
    userId: text().notNull(),
  },
  (table) => [
    uniqueIndex("property_addressId_key").using(
      "btree",
      table.addressId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.addressId],
      foreignColumns: [address.id],
      name: "property_addressId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [user.id],
      name: "property_userId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("restrict"),
  ],
);

export const job = pgTable(
  "job",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    propertyId: text().notNull(),
    budget: integer().notNull(),
    taskBids: boolean().default(false).notNull(),
    startsAt: timestamp().notNull(),
    deadline: timestamp().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
    status: jobStatus().default("DRAFT").notNull(),
    completedAt: timestamp(),
    contractorCompleted: boolean().default(false).notNull(),
    homeownerCompleted: boolean().default(false).notNull(),
    isRecurring: boolean().default(false).notNull(),
    jobType: jobType().default("STANDARD").notNull(),
    recurringFrequency: text(),
  },
  (table) => [
    foreignKey({
      columns: [table.propertyId],
      foreignColumns: [property.id],
      name: "job_propertyId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const address = pgTable("address", {
  id: text()
    .primaryKey()
    .notNull()
    .$defaultFn(() => createId()),
  street: text().notNull(),
  city: text().notNull(),
  state: text().notNull(),
  zip: text().notNull(),
  createdAt: timestamp().defaultNow().notNull(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
  location: geometry("location", {
    type: "point",
    mode: "xy",
  }),
});

export const collaboration = pgTable(
  "collaboration",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    primaryOrgId: text().notNull(),
    notes: text(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
    crewMemberId: text().notNull(),
  },
  (table) => [
    index("collaboration_crewMemberId_idx").using(
      "btree",
      table.crewMemberId.asc().nullsLast().op("text_ops"),
    ),
    uniqueIndex("collaboration_primaryOrgId_crewMemberId_key").using(
      "btree",
      table.primaryOrgId.asc().nullsLast().op("text_ops"),
      table.crewMemberId.asc().nullsLast().op("text_ops"),
    ),
    index("collaboration_primaryOrgId_idx").using(
      "btree",
      table.primaryOrgId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.primaryOrgId],
      foreignColumns: [organization.id],
      name: "collaboration_primaryOrgId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.crewMemberId],
      foreignColumns: [organization.id],
      name: "collaboration_crewMemberId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const jobTemplate = pgTable("job_template", {
  id: text()
    .primaryKey()
    .notNull()
    .$defaultFn(() => createId()),
  name: text().notNull(),
  description: text().notNull(),
  budget: integer().notNull(),
  estimatedDuration: integer().default(7).notNull(),
  createdAt: timestamp().defaultNow().notNull(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const templateTask = pgTable(
  "template_task",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    templateId: text().notNull(),
    tradeId: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .$onUpdate(() => new Date())
      .defaultNow(),
  },
  (table) => [
    index("template_task_templateId_idx").using(
      "btree",
      table.templateId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.templateId],
      foreignColumns: [jobTemplate.id],
      name: "template_task_templateId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.tradeId],
      foreignColumns: [trade.id],
      name: "template_task_tradeId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const review = pgTable(
  "review",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    jobId: text().notNull(),
    rating: integer().notNull(),
    comment: text().notNull(),
    reviewType: text().notNull(),
    reviewerId: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index("review_jobId_idx").using(
      "btree",
      table.jobId.asc().nullsLast().op("text_ops"),
    ),
    index("review_reviewerId_idx").using(
      "btree",
      table.reviewerId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.jobId],
      foreignColumns: [job.id],
      name: "review_jobId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const pushSubscription = pgTable(
  "push_subscription",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    endpoint: text().notNull(),
    p256dh: text().notNull(),
    auth: text().notNull(),
    accountId: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    index("push_subscription_accountId_idx").using(
      "btree",
      table.accountId.asc().nullsLast().op("text_ops"),
    ),
    uniqueIndex("push_subscription_endpoint_key").using(
      "btree",
      table.endpoint.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.accountId],
      foreignColumns: [account.id],
      name: "push_subscription_accountId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const service = pgTable(
  "service",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    name: text().notNull(),
    description: text(),
    price: doublePrecision().notNull(),
    duration: integer(),
    organizationId: text().notNull(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
  },
  (table) => [
    foreignKey({
      columns: [table.organizationId],
      foreignColumns: [organization.id],
      name: "service_organizationId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const schedule = pgTable(
  "schedule",
  {
    id: text()
      .primaryKey()
      .notNull()
      .$defaultFn(() => createId()),
    jobId: text().notNull(),
    proposedStartDate: timestamp().notNull(),
    proposedEndDate: timestamp().notNull(),
    confirmedStartDate: timestamp(),
    confirmedEndDate: timestamp(),
    status: scheduleStatus().default("PROPOSED").notNull(),
    proposedById: text().notNull(),
    proposedByRole: text().notNull(),
    notes: text(),
    createdAt: timestamp().defaultNow().notNull(),
    updatedAt: timestamp()
      .notNull()
      .defaultNow()
      .$onUpdate(() => new Date()),
    completed: boolean().default(false).notNull(),
    completedAt: timestamp(),
  },
  (table) => [
    index("schedule_jobId_idx").using(
      "btree",
      table.jobId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.jobId],
      foreignColumns: [job.id],
      name: "schedule_jobId_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
  ],
);

export const bidToTask = pgTable(
  "_bid_to_task",
  {
    a: text("A").notNull(),
    b: text("B").notNull(),
  },
  (table) => [
    index().using("btree", table.b.asc().nullsLast().op("text_ops")),
    foreignKey({
      columns: [table.a],
      foreignColumns: [bid.id],
      name: "_bid_to_task_A_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    foreignKey({
      columns: [table.b],
      foreignColumns: [task.id],
      name: "_bid_to_task_B_fkey",
    })
      .onUpdate("cascade")
      .onDelete("cascade"),
    primaryKey({ columns: [table.a, table.b], name: "_bid_to_task_AB_pkey" }),
  ],
);

export const membership = pgTable(
  "membership",
  {
    userId: text().notNull(),
    organizationId: text().notNull(),
    role: text().notNull(),
  },
  (table) => [
    uniqueIndex("membership_userId_organizationId_key").using(
      "btree",
      table.userId.asc().nullsLast().op("text_ops"),
      table.organizationId.asc().nullsLast().op("text_ops"),
    ),
    foreignKey({
      columns: [table.organizationId],
      foreignColumns: [organization.id],
      name: "membership_organizationId_fkey",
    }),
    primaryKey({
      columns: [table.userId, table.organizationId],
      name: "membership_pkey",
    }),
    foreignKey({
      columns: [table.userId],
      foreignColumns: [user.id],
      name: "membership_userId_fkey",
    }),
  ],
);

export const resources = pgTable("resources", {
  id: text()
    .primaryKey()
    .$defaultFn(() => createId()),
  content: text().notNull(),
  createdAt: timestamp().notNull().defaultNow(),
  updatedAt: timestamp()
    .notNull()
    .defaultNow()
    .$onUpdate(() => new Date()),
});

export const embeddings = pgTable(
  "embeddings",
  {
    id: text()
      .primaryKey()
      .$defaultFn(() => createId()),
    resourceId: text().references(() => resources.id, { onDelete: "cascade" }),
    content: text().notNull(),
    embedding: vector("embedding", { dimensions: 1536 }).notNull(),
  },
  (table) => [
    index("embeddings_embedding_idx").using(
      "hnsw",
      table.embedding.op("vector_cosine_ops"),
    ),
  ],
);

export const aiChat = pgTable("ai_chat", {
  id: text()
    .primaryKey()
    .$defaultFn(() => createId()),
});

export const aiChatRole = pgEnum("ai_chat_role", [
  "user",
  "assistant",
  "system",
  "data",
]);

export const aiMessage = pgTable("ai_message", {
  id: text().primaryKey().notNull(),
  chatId: text().notNull(),
  createdAt: timestamp().notNull().defaultNow(),
  parts: jsonb().$type<UIMessage["parts"]>().notNull(),
  role: aiChatRole().notNull(),
});

export const taskRelations = relations(task, ({ one, many }) => ({
  job: one(job, {
    fields: [task.jobId],
    references: [job.id],
  }),
  trade: one(trade, {
    fields: [task.tradeId],
    references: [trade.id],
  }),
  bidToTasks: many(bidToTask),
}));

export const jobRelations = relations(job, ({ one, many }) => ({
  tasks: many(task),
  chats: many(chat),
  images: many(jobImage),
  bids: many(bid),
  property: one(property, {
    fields: [job.propertyId],
    references: [property.id],
  }),
  reviews: many(review),
  schedules: many(schedule),
}));

export const tradeRelations = relations(trade, ({ many }) => ({
  tasks: many(task),
  organizations: many(organization),
  templateTasks: many(templateTask),
}));

export const organizationRelations = relations(
  organization,
  ({ one, many }) => ({
    trade: one(trade, {
      fields: [organization.tradeId],
      references: [trade.id],
    }),
    address: one(address, {
      fields: [organization.addressId],
      references: [address.id],
    }),
    bids: many(bid),
    collaborations_primaryOrgId: many(collaboration, {
      relationName: "collaboration_primaryOrgId_organization_id",
    }),
    collaborations_crewMemberId: many(collaboration, {
      relationName: "collaboration_crewMemberId_organization_id",
    }),
    services: many(service),
    memberships: many(membership),
  }),
);

export const addressRelations = relations(address, ({ many }) => ({
  organizations: many(organization),
  properties: many(property),
}));

export const chatRelations = relations(chat, ({ one, many }) => ({
  bid: one(bid, {
    fields: [chat.bidId],
    references: [bid.id],
  }),
  job: one(job, {
    fields: [chat.jobId],
    references: [job.id],
  }),
  messages: many(message),
}));

export const bidRelations = relations(bid, ({ one, many }) => ({
  chats: many(chat),
  job: one(job, {
    fields: [bid.jobId],
    references: [job.id],
  }),
  organization: one(organization, {
    fields: [bid.organizationId],
    references: [organization.id],
  }),
  bidToTasks: many(bidToTask),
}));

export const messageRelations = relations(message, ({ one }) => ({
  chat: one(chat, {
    fields: [message.chatId],
    references: [chat.id],
  }),
}));

export const jobImageRelations = relations(jobImage, ({ one }) => ({
  job: one(job, {
    fields: [jobImage.jobId],
    references: [job.id],
  }),
}));

export const propertyRelations = relations(property, ({ one, many }) => ({
  address: one(address, {
    fields: [property.addressId],
    references: [address.id],
  }),
  user: one(user, {
    fields: [property.userId],
    references: [user.id],
  }),
  jobs: many(job),
}));

export const collaborationRelations = relations(collaboration, ({ one }) => ({
  primaryOrg: one(organization, {
    fields: [collaboration.primaryOrgId],
    references: [organization.id],
    relationName: "collaboration_primaryOrgId_organization_id",
  }),
  crewMember: one(organization, {
    fields: [collaboration.crewMemberId],
    references: [organization.id],
    relationName: "collaboration_crewMemberId_organization_id",
  }),
}));

export const templateTaskRelations = relations(templateTask, ({ one }) => ({
  jobTemplate: one(jobTemplate, {
    fields: [templateTask.templateId],
    references: [jobTemplate.id],
  }),
  trade: one(trade, {
    fields: [templateTask.tradeId],
    references: [trade.id],
  }),
}));

export const jobTemplateRelations = relations(jobTemplate, ({ many }) => ({
  tasks: many(templateTask),
}));

export const reviewRelations = relations(review, ({ one }) => ({
  job: one(job, {
    fields: [review.jobId],
    references: [job.id],
  }),
}));

export const pushSubscriptionRelations = relations(
  pushSubscription,
  ({ one }) => ({
    account: one(account, {
      fields: [pushSubscription.accountId],
      references: [account.id],
    }),
  }),
);

export const serviceRelations = relations(service, ({ one }) => ({
  organization: one(organization, {
    fields: [service.organizationId],
    references: [organization.id],
  }),
}));

export const scheduleRelations = relations(schedule, ({ one }) => ({
  job: one(job, {
    fields: [schedule.jobId],
    references: [job.id],
  }),
}));

export const bidToTaskRelations = relations(bidToTask, ({ one }) => ({
  bid: one(bid, {
    fields: [bidToTask.a],
    references: [bid.id],
  }),
  task: one(task, {
    fields: [bidToTask.b],
    references: [task.id],
  }),
}));

export const membershipRelations = relations(membership, ({ one }) => ({
  organization: one(organization, {
    fields: [membership.organizationId],
    references: [organization.id],
  }),
}));

export type Account = InferSelectModel<typeof account>;
export type Membership = InferSelectModel<typeof membership>;
export type Organization = InferSelectModel<typeof organization> & {
  address?: Address;
  trade?: Trade | null | undefined;
  services?: Service[];
};
export type Property = InferSelectModel<typeof property> & {
  address?: Address;
};
export type Address = InferSelectModel<typeof address>;
export type Job = InferSelectModel<typeof job> & {
  property?: Property | null | undefined;
  tasks?: Task[];
  bids?: Bid[] | null | undefined;
  images?: JobImage[] | undefined;
  schedules?: Schedule[] | null | undefined;
  reviews?: Review[] | null | undefined;
  distance?: number;
  bidsCount?: number;
};
export type Bid = InferSelectModel<typeof bid>;
export type Schedule = InferSelectModel<typeof schedule>;
export type PushSubscription = InferSelectModel<typeof pushSubscription>;
export type Chat = InferSelectModel<typeof chat>;
export type Message = InferSelectModel<typeof message>;
export type Review = InferSelectModel<typeof review>;
export type Task = InferSelectModel<typeof task>;
export type JobImage = InferSelectModel<typeof jobImage>;
export type Trade = InferSelectModel<typeof trade> & {
  organizations?: Organization[];
  organizationCount?: number;
};
export type Service = InferSelectModel<typeof service>;
export type Collaboration = InferSelectModel<typeof collaboration> & {
  primaryOrg: Organization;
  crewMember: Organization;
};
export type TemplateTask = InferSelectModel<typeof templateTask> & {
  trade: Trade;
};
export type JobTemplate = InferSelectModel<typeof jobTemplate> & {
  tasks: TemplateTask[];
};
export type JobStatus = InferEnum<typeof jobStatus>;
export type BidStatus = InferEnum<typeof bidStatus>;
export type JobType = InferEnum<typeof jobType>;
export type ScheduleStatus = InferEnum<typeof scheduleStatus>;
export type User = InferSelectModel<typeof user>;
