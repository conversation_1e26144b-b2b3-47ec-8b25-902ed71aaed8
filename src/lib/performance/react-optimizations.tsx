"use client";

/**
 * React Performance Optimization Hooks and Components
 * 
 * This module provides React-specific performance optimizations including:
 * - Memoization utilities
 * - Virtual scrolling
 * - Intersection observer hooks
 * - Performance monitoring hooks
 */

import { 
  useCallback, 
  useEffect, 
  useMemo, 
  useRef, 
  useState,
  type RefObject,
  type ComponentType,
  memo,
  forwardRef,
  type ForwardedRef
} from "react";

// ============================================================================
// MEMOIZATION UTILITIES
// ============================================================================

/**
 * Enhanced useMemo with dependency tracking
 */
export function useStableMemo<T>(
  factory: () => T,
  deps: React.DependencyList,
  debugName?: string
): T {
  const prevDeps = useRef<React.DependencyList>();
  const result = useRef<T>();

  const depsChanged = !prevDeps.current || 
    deps.length !== prevDeps.current.length ||
    deps.some((dep, index) => dep !== prevDeps.current![index]);

  if (depsChanged) {
    if (debugName && process.env.NODE_ENV === "development") {
      console.log(`🔄 Recomputing ${debugName}`);
    }
    result.current = factory();
    prevDeps.current = deps;
  }

  return result.current!;
}

/**
 * Stable callback that doesn't change unless dependencies change
 */
export function useStableCallback<T extends (...args: any[]) => any>(
  callback: T,
  deps: React.DependencyList
): T {
  return useCallback(callback, deps);
}

/**
 * Memoized component wrapper with display name
 */
export function createMemoComponent<P extends object>(
  Component: ComponentType<P>,
  displayName?: string,
  areEqual?: (prevProps: P, nextProps: P) => boolean
) {
  const MemoizedComponent = memo(Component, areEqual);
  MemoizedComponent.displayName = displayName || `Memo(${Component.displayName || Component.name})`;
  return MemoizedComponent;
}

// ============================================================================
// INTERSECTION OBSERVER HOOKS
// ============================================================================

/**
 * Hook for intersection observer with performance optimizations
 */
export function useIntersectionObserver(
  options: IntersectionObserverInit & {
    freezeOnceVisible?: boolean;
    triggerOnce?: boolean;
  } = {}
) {
  const [entry, setEntry] = useState<IntersectionObserverEntry>();
  const [isVisible, setIsVisible] = useState(false);
  const elementRef = useRef<HTMLElement>();
  const observer = useRef<IntersectionObserver>();

  const { freezeOnceVisible = false, triggerOnce = false, ...observerOptions } = options;

  const frozen = entry?.isIntersecting && freezeOnceVisible;

  const updateEntry = useCallback((entries: IntersectionObserverEntry[]) => {
    const [entry] = entries;
    if (entry) {
      setEntry(entry);
      setIsVisible(entry.isIntersecting);
      
      if (triggerOnce && entry.isIntersecting && observer.current) {
        observer.current.disconnect();
      }
    }
  }, [triggerOnce]);

  useEffect(() => {
    const element = elementRef.current;
    if (!element || frozen) return;

    observer.current = new IntersectionObserver(updateEntry, observerOptions);
    observer.current.observe(element);

    return () => observer.current?.disconnect();
  }, [updateEntry, frozen, observerOptions]);

  const ref = useCallback((element: HTMLElement | null) => {
    if (element) {
      elementRef.current = element;
    }
  }, []);

  return { ref, entry, isVisible };
}

/**
 * Hook for lazy loading with intersection observer
 */
export function useLazyLoad<T>(
  loadFn: () => Promise<T>,
  options: {
    rootMargin?: string;
    threshold?: number;
    triggerOnce?: boolean;
  } = {}
) {
  const [data, setData] = useState<T | null>(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);
  
  const { ref, isVisible } = useIntersectionObserver({
    rootMargin: options.rootMargin || "50px",
    threshold: options.threshold || 0.1,
    triggerOnce: options.triggerOnce ?? true,
  });

  useEffect(() => {
    if (isVisible && !data && !loading) {
      setLoading(true);
      setError(null);
      
      loadFn()
        .then(setData)
        .catch(setError)
        .finally(() => setLoading(false));
    }
  }, [isVisible, data, loading, loadFn]);

  return { ref, data, loading, error, isVisible };
}

// ============================================================================
// VIRTUAL SCROLLING
// ============================================================================

/**
 * Virtual scrolling hook for large lists
 */
export function useVirtualScroll<T>({
  items,
  itemHeight,
  containerHeight,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  containerHeight: number;
  overscan?: number;
}) {
  const [scrollTop, setScrollTop] = useState(0);
  const scrollElementRef = useRef<HTMLDivElement>(null);

  const startIndex = Math.max(0, Math.floor(scrollTop / itemHeight) - overscan);
  const endIndex = Math.min(
    items.length - 1,
    Math.ceil((scrollTop + containerHeight) / itemHeight) + overscan
  );

  const visibleItems = useMemo(() => {
    return items.slice(startIndex, endIndex + 1).map((item, index) => ({
      item,
      index: startIndex + index,
    }));
  }, [items, startIndex, endIndex]);

  const totalHeight = items.length * itemHeight;
  const offsetY = startIndex * itemHeight;

  const handleScroll = useCallback((e: React.UIEvent<HTMLDivElement>) => {
    setScrollTop(e.currentTarget.scrollTop);
  }, []);

  return {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
  };
}

// ============================================================================
// PERFORMANCE MONITORING HOOKS
// ============================================================================

/**
 * Hook to measure component render performance
 */
export function useRenderPerformance(componentName: string, enabled = process.env.NODE_ENV === "development") {
  const renderCount = useRef(0);
  const lastRenderTime = useRef(0);

  useEffect(() => {
    if (!enabled) return;

    renderCount.current += 1;
    const now = performance.now();
    
    if (lastRenderTime.current > 0) {
      const timeSinceLastRender = now - lastRenderTime.current;
      console.log(`🎭 ${componentName} render #${renderCount.current} (${timeSinceLastRender.toFixed(2)}ms since last)`);
    }
    
    lastRenderTime.current = now;
  });

  return {
    renderCount: renderCount.current,
  };
}

/**
 * Hook to track expensive computations
 */
export function useComputationTracker<T>(
  computation: () => T,
  deps: React.DependencyList,
  name?: string
): T {
  return useMemo(() => {
    const start = performance.now();
    const result = computation();
    const duration = performance.now() - start;
    
    if (name && duration > 10) { // Log computations taking more than 10ms
      console.log(`🧮 Expensive computation "${name}": ${duration.toFixed(2)}ms`);
    }
    
    return result;
  }, deps);
}

/**
 * Hook to debounce expensive operations
 */
export function useDebounce<T>(value: T, delay: number): T {
  const [debouncedValue, setDebouncedValue] = useState<T>(value);

  useEffect(() => {
    const handler = setTimeout(() => {
      setDebouncedValue(value);
    }, delay);

    return () => {
      clearTimeout(handler);
    };
  }, [value, delay]);

  return debouncedValue;
}

/**
 * Hook to throttle expensive operations
 */
export function useThrottle<T extends (...args: any[]) => any>(
  callback: T,
  delay: number
): T {
  const lastCall = useRef(0);
  const timeoutRef = useRef<NodeJS.Timeout>();

  return useCallback((...args: Parameters<T>) => {
    const now = Date.now();
    
    if (now - lastCall.current >= delay) {
      lastCall.current = now;
      return callback(...args);
    } else {
      clearTimeout(timeoutRef.current);
      timeoutRef.current = setTimeout(() => {
        lastCall.current = Date.now();
        callback(...args);
      }, delay - (now - lastCall.current));
    }
  }, [callback, delay]) as T;
}

// ============================================================================
// PERFORMANCE COMPONENTS
// ============================================================================

/**
 * Lazy loading wrapper component
 */
export const LazyWrapper = memo(forwardRef<
  HTMLDivElement,
  {
    children: React.ReactNode;
    fallback?: React.ReactNode;
    rootMargin?: string;
    threshold?: number;
    className?: string;
  }
>(({ children, fallback, rootMargin, threshold, className }, ref) => {
  const { ref: intersectionRef, isVisible } = useIntersectionObserver({
    rootMargin,
    threshold,
    triggerOnce: true,
  });

  const combinedRef = useCallback((element: HTMLDivElement | null) => {
    intersectionRef(element);
    if (typeof ref === 'function') {
      ref(element);
    } else if (ref) {
      ref.current = element;
    }
  }, [intersectionRef, ref]);

  return (
    <div ref={combinedRef} className={className}>
      {isVisible ? children : fallback}
    </div>
  );
}));

LazyWrapper.displayName = "LazyWrapper";

/**
 * Virtual list component
 */
export const VirtualList = memo(<T,>({
  items,
  itemHeight,
  height,
  renderItem,
  className,
  overscan = 5,
}: {
  items: T[];
  itemHeight: number;
  height: number;
  renderItem: (item: T, index: number) => React.ReactNode;
  className?: string;
  overscan?: number;
}) => {
  const {
    scrollElementRef,
    visibleItems,
    totalHeight,
    offsetY,
    handleScroll,
  } = useVirtualScroll({
    items,
    itemHeight,
    containerHeight: height,
    overscan,
  });

  return (
    <div
      ref={scrollElementRef}
      className={className}
      style={{ height, overflow: 'auto' }}
      onScroll={handleScroll}
    >
      <div style={{ height: totalHeight, position: 'relative' }}>
        <div style={{ transform: `translateY(${offsetY}px)` }}>
          {visibleItems.map(({ item, index }) => (
            <div key={index} style={{ height: itemHeight }}>
              {renderItem(item, index)}
            </div>
          ))}
        </div>
      </div>
    </div>
  );
});

VirtualList.displayName = "VirtualList";
