/**
 * Performance Optimization Utilities
 * 
 * This module provides comprehensive performance optimization utilities including:
 * - Dynamic imports and code splitting
 * - Prefetching strategies
 * - Performance monitoring
 * - Bundle optimization helpers
 */

import { type ComponentType, lazy, type LazyExoticComponent } from "react";

// ============================================================================
// DYNAMIC IMPORTS & CODE SPLITTING
// ============================================================================

/**
 * Enhanced lazy loading with error boundaries and loading states
 */
export function createLazyComponent<T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  options: {
    fallback?: React.ComponentType;
    errorFallback?: React.ComponentType<{ error: Error; retry: () => void }>;
    preload?: boolean;
  } = {}
): LazyExoticComponent<T> & { preload: () => Promise<{ default: T }> } {
  const LazyComponent = lazy(importFn);
  
  // Add preload method
  (LazyComponent as any).preload = importFn;
  
  // Preload immediately if requested
  if (options.preload) {
    importFn().catch(() => {
      // Silently fail preload attempts
    });
  }
  
  return LazyComponent as LazyExoticComponent<T> & { preload: () => Promise<{ default: T }> };
}

/**
 * Route-based code splitting helper
 */
export const createRouteComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  routeName: string
) => {
  return createLazyComponent(importFn, {
    preload: false, // Routes are loaded on demand
  });
};

/**
 * Feature-based code splitting helper
 */
export const createFeatureComponent = <T extends ComponentType<any>>(
  importFn: () => Promise<{ default: T }>,
  featureName: string,
  preload = false
) => {
  return createLazyComponent(importFn, {
    preload,
  });
};

// ============================================================================
// PREFETCHING STRATEGIES
// ============================================================================

/**
 * Intersection Observer based prefetching
 */
export class ViewportPrefetcher {
  private observer: IntersectionObserver | null = null;
  private prefetchedRoutes = new Set<string>();

  constructor(
    private options: {
      rootMargin?: string;
      threshold?: number;
      maxPrefetches?: number;
    } = {}
  ) {
    if (typeof window !== "undefined") {
      this.observer = new IntersectionObserver(
        this.handleIntersection.bind(this),
        {
          rootMargin: options.rootMargin || "50px",
          threshold: options.threshold || 0.1,
        }
      );
    }
  }

  private handleIntersection(entries: IntersectionObserverEntry[]) {
    entries.forEach((entry) => {
      if (entry.isIntersecting) {
        const element = entry.target as HTMLElement;
        const href = element.getAttribute("data-prefetch-href");
        
        if (href && !this.prefetchedRoutes.has(href)) {
          this.prefetchRoute(href);
          this.observer?.unobserve(element);
        }
      }
    });
  }

  private async prefetchRoute(href: string) {
    if (this.prefetchedRoutes.size >= (this.options.maxPrefetches || 10)) {
      return;
    }

    try {
      // Use Next.js router prefetch
      const { useRouter } = await import("next/navigation");
      // Note: This is a simplified version - in practice you'd need to handle this differently
      this.prefetchedRoutes.add(href);
      console.log(`🚀 Prefetched route: ${href}`);
    } catch (error) {
      console.warn(`Failed to prefetch route: ${href}`, error);
    }
  }

  observe(element: HTMLElement, href: string) {
    if (this.observer && element) {
      element.setAttribute("data-prefetch-href", href);
      this.observer.observe(element);
    }
  }

  disconnect() {
    this.observer?.disconnect();
  }
}

/**
 * User behavior based prefetching
 */
export class BehaviorPrefetcher {
  private hoverTimer: NodeJS.Timeout | null = null;
  private prefetchedRoutes = new Set<string>();

  constructor(
    private options: {
      hoverDelay?: number;
      maxPrefetches?: number;
    } = {}
  ) {}

  onLinkHover(href: string, callback?: () => void) {
    if (this.prefetchedRoutes.has(href)) {
      return;
    }

    this.hoverTimer = setTimeout(() => {
      this.prefetchRoute(href);
      callback?.();
    }, this.options.hoverDelay || 300);
  }

  onLinkLeave() {
    if (this.hoverTimer) {
      clearTimeout(this.hoverTimer);
      this.hoverTimer = null;
    }
  }

  private async prefetchRoute(href: string) {
    if (this.prefetchedRoutes.size >= (this.options.maxPrefetches || 15)) {
      return;
    }

    try {
      this.prefetchedRoutes.add(href);
      console.log(`🎯 Behavior-prefetched route: ${href}`);
    } catch (error) {
      console.warn(`Failed to prefetch route: ${href}`, error);
    }
  }
}

// ============================================================================
// PERFORMANCE MONITORING
// ============================================================================

/**
 * Performance metrics collector
 */
export class PerformanceMonitor {
  private metrics: Map<string, number[]> = new Map();
  private observers: PerformanceObserver[] = [];

  constructor() {
    if (typeof window !== "undefined") {
      this.initializeObservers();
    }
  }

  private initializeObservers() {
    // Measure navigation timing
    if ("PerformanceObserver" in window) {
      const navObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (entry.entryType === "navigation") {
            const navEntry = entry as PerformanceNavigationTiming;
            this.recordMetric("navigation.domContentLoaded", navEntry.domContentLoadedEventEnd - navEntry.domContentLoadedEventStart);
            this.recordMetric("navigation.load", navEntry.loadEventEnd - navEntry.loadEventStart);
          }
        }
      });
      
      navObserver.observe({ entryTypes: ["navigation"] });
      this.observers.push(navObserver);
    }

    // Measure resource loading
    const resourceObserver = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === "resource") {
          const resourceEntry = entry as PerformanceResourceTiming;
          this.recordMetric(`resource.${this.getResourceType(resourceEntry.name)}`, resourceEntry.duration);
        }
      }
    });
    
    resourceObserver.observe({ entryTypes: ["resource"] });
    this.observers.push(resourceObserver);
  }

  private getResourceType(url: string): string {
    if (url.includes(".js")) return "javascript";
    if (url.includes(".css")) return "stylesheet";
    if (url.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) return "image";
    if (url.includes("api/")) return "api";
    return "other";
  }

  recordMetric(name: string, value: number) {
    if (!this.metrics.has(name)) {
      this.metrics.set(name, []);
    }
    this.metrics.get(name)!.push(value);
  }

  getMetrics() {
    const summary: Record<string, { avg: number; min: number; max: number; count: number }> = {};
    
    for (const [name, values] of this.metrics.entries()) {
      summary[name] = {
        avg: values.reduce((a, b) => a + b, 0) / values.length,
        min: Math.min(...values),
        max: Math.max(...values),
        count: values.length,
      };
    }
    
    return summary;
  }

  disconnect() {
    this.observers.forEach(observer => observer.disconnect());
  }
}

// ============================================================================
// BUNDLE OPTIMIZATION HELPERS
// ============================================================================

/**
 * Check if code splitting is supported
 */
export const isCodeSplittingSupported = () => {
  return typeof window !== "undefined" && "import" in window;
};

/**
 * Preload critical resources
 */
export const preloadCriticalResources = (resources: string[]) => {
  if (typeof window === "undefined") return;

  resources.forEach((resource) => {
    const link = document.createElement("link");
    link.rel = "preload";
    link.href = resource;
    
    if (resource.endsWith(".js")) {
      link.as = "script";
    } else if (resource.endsWith(".css")) {
      link.as = "style";
    } else if (resource.match(/\.(jpg|jpeg|png|gif|webp|svg)$/)) {
      link.as = "image";
    }
    
    document.head.appendChild(link);
  });
};

// ============================================================================
// EXPORTS
// ============================================================================

export const performanceUtils = {
  createLazyComponent,
  createRouteComponent,
  createFeatureComponent,
  ViewportPrefetcher,
  BehaviorPrefetcher,
  PerformanceMonitor,
  isCodeSplittingSupported,
  preloadCriticalResources,
};

// Global performance monitor instance
export const globalPerformanceMonitor = new PerformanceMonitor();
