import { format } from "date-fns";
import { and, eq } from "drizzle-orm";
import ScheduledJobReminderEmail from "@/components/emails/scheduled-job-reminder";
import { db } from "@/db";
import { bid, job, membership, user } from "@/db/schema";
import { sendEmail } from "@/lib/email";

export async function sendScheduledJobReminder(jobId: string) {
  // Get the job details with schedule
  const jobData = await db.query.job.findFirst({
    where: eq(job.id, jobId),
    with: {
      property: {
        with: {
          address: true,
        },
      },
      schedules: true,
      bids: {
        where: eq(bid.status, "ACCEPTED"),
        with: {
          organization: true,
        },
      },
    },
  });

  if (
    !jobData ||
    !jobData.schedules ||
    jobData.schedules[0]?.status !== "CONFIRMED"
  )
    return;

  // Get location string
  const location = jobData.property.address
    ? `${jobData.property.address.city}, ${jobData.property.address.state}`
    : jobData.property.name;

  // Format date and time
  const scheduledDate = format(
    jobData.schedules[0]?.confirmedStartDate as Date,
    "PPP",
  );
  const scheduledTime = `${format(jobData.schedules[0].confirmedStartDate as Date, "h:mm a")} - ${format(jobData.schedules[0].confirmedEndDate as Date, "h:mm a")}`;

  // Find property owner
  const propertyOwner = await db.query.user.findFirst({
    where: eq(user.id, jobData.property.userId),
  });

  if (!propertyOwner) return;

  // For quick hire jobs or jobs with accepted bids
  let contractorUserId: string | undefined;
  let contractorOrgName = "Contractor";

  if (jobData.jobType === "QUICK_HIRE" && jobData.bids.length > 0) {
    // Get the contractor from the accepted bid
    const acceptedBid = jobData.bids[0];
    contractorOrgName = acceptedBid?.organization.name || "Contractor";

    if (!acceptedBid) return;

    // Find the contractor user
    const contractorMembership = await db.query.membership.findFirst({
      where: and(
        eq(membership.organizationId, acceptedBid.organizationId),
        eq(membership.role, "OWNER"),
      ),
      columns: {
        userId: true,
      },
    });

    if (contractorMembership) {
      contractorUserId = contractorMembership.userId;
    }
  }

  // Send email to homeowner
  if (propertyOwner.email) {
    await sendEmail({
      to: propertyOwner.email,
      subject: `Reminder: Your scheduled job "${jobData.name}" is coming up`,
      template: ScheduledJobReminderEmail({
        jobName: jobData.name,
        jobId: jobData.id,
        scheduledDate,
        scheduledTime,
        location,
        recipientRole: "HOMEOWNER",
        counterpartyName: contractorOrgName,
        userId: propertyOwner.id,
        recipientEmail: propertyOwner.email,
      }),
    });
  }

  // Send email to contractor if available
  if (contractorUserId) {
    const contractor = await db.query.user.findFirst({
      where: eq(user.id, contractorUserId),
    });

    if (contractor?.email) {
      await sendEmail({
        to: contractor.email,
        subject: `Reminder: Your scheduled job "${jobData.name}" is coming up`,
        template: ScheduledJobReminderEmail({
          jobName: jobData.name,
          jobId: jobData.id,
          scheduledDate,
          scheduledTime,
          location,
          recipientRole: "PROFESSIONAL",
          counterpartyName: propertyOwner.name,
          userId: contractorUserId,
          recipientEmail: contractor.email,
        }),
      });
    }
  }
}
