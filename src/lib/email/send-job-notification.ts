import { eq, inArray } from "drizzle-orm";
import NewJobNotificationEmail from "@/components/emails/new-job-notification";
import { db } from "@/db";
import { job, organization, user } from "@/db/schema";
import { sendEmail } from "@/lib/email";

export async function sendJobNotificationEmails(
  jobId: string,
  tradeIds: string[],
) {
  // Get the job details
  const jobData = await db.query.job.findFirst({
    where: eq(job.id, jobId),
    with: {
      property: {
        with: {
          address: true,
        },
      },
      tasks: {
        with: {
          trade: true,
        },
      },
    },
  });

  if (!jobData) return;

  // Find organizations with matching trades
  const organizationList = await db.query.organization.findMany({
    where: inArray(organization.tradeId, tradeIds),
    with: {
      trade: true,
      memberships: {
        columns: {
          userId: true,
        },
      },
    },
  });

  // Get location string
  const location = jobData.property.address
    ? `${jobData.property.address.city}, ${jobData.property.address.state}`
    : "Location not specified";

  // Format deadline
  const deadline = jobData.deadline.toLocaleDateString();

  // Send email to each organization member
  for (const org of organizationList) {
    for (const membership of org.memberships) {
      const userData = await db.query.user.findFirst({
        where: eq(user.id, membership.userId),
      });

      await sendEmail({
        to: userData?.email as string,
        subject: `New ${org.trade?.name} Job Available: ${jobData.name}`,
        template: NewJobNotificationEmail({
          jobName: jobData.name,
          jobId: jobData.id,
          tradeName: org.trade?.name as string,
          budget: jobData.budget,
          location,
          deadline,
          userId: membership.userId,
          recipientEmail: userData?.email as string,
        }),
      });
    }
  }
}
