import PasswordResetEmail from "@/components/emails/password-reset";
import { sendEmail } from "@/lib/email";

export async function sendPasswordReset({
  email,
  userId,
  resetCode,
  resetLink,
}: {
  email: string;
  userId?: string;
  resetCode: string;
  resetLink: string;
}) {
  try {
    // Send the password reset email
    await sendEmail({
      to: email,
      subject: "Reset your password for TradeCrews",
      template: PasswordResetEmail({
        resetCode,
        resetLink,
        userId,
        recipientEmail: email,
      }),
    });

    return { success: true };
  } catch (error) {
    console.error("Error sending password reset email:", error);
    return { success: false, error };
  }
}
