import { eq } from "drizzle-orm";
import NewBidNotificationEmail from "@/components/emails/new-bid-notification";
import { db } from "@/db";
import { bid, user } from "@/db/schema";
import { sendEmail } from "@/lib/email";

export async function sendBidNotificationEmail(bidId: string) {
  // Get the bid details
  const bidData = await db.query.bid.findFirst({
    where: eq(bid.id, bidId),
    with: {
      job: {
        with: {
          property: true,
        },
      },
      organization: true,
    },
  });

  if (!bidData) return;

  // Find the property owner
  const propertyOwner = await db.query.user.findFirst({
    where: eq(user.id, bidData.job.property.userId),
  });

  if (!propertyOwner) return;

  // Send the notification email
  await sendEmail({
    to: propertyOwner.email,
    subject: `New Bid Received: ${bidData.name}`,
    template: NewBidNotificationEmail({
      bidName: bidData.name,
      bidId: bidData.id,
      jobName: bidData.job.name,
      contractorName: bidData.organization.name,
      amount: bidData.amount,
      estimatedDuration: bidData.estimatedDuration || 0,
      userId: propertyOwner.id,
      recipientEmail: propertyOwner.email,
    }),
  });
}
