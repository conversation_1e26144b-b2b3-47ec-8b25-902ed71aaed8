import { initTRPC, TRPCError } from "@trpc/server";
import superjson from "superjson";
import type { Context } from "../core/context";
import type { ResourceType, PermissionAction, UserRole, OrganizationRole } from "../types/permissions";
import {
  createPermissionMiddleware,
  requireRole,
  requireOrganizationRole,
  requireAdmin,
  requireContractor,
  requireHomeowner,
  requireOrganizationOwner,
  requireOrganizationAdmin,
  jobMiddlewares,
  bidMiddlewares,
  propertyMiddlewares,
  organizationMiddlewares,
  messageMiddlewares,
  reviewMiddlewares,
  scheduleMiddlewares,
  templateMiddlewares,
} from "../middleware/permissions";

// ============================================================================
// TRPC INITIALIZATION
// ============================================================================

const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// ============================================================================
// BASE MIDDLEWARES
// ============================================================================

/**
 * Authentication middleware
 */
const isAuthed = t.middleware(({ next, ctx }) => {
  if (!ctx.userId) {
    throw new TRPCError({ code: "UNAUTHORIZED" });
  }

  return next({
    ctx: {
      userId: ctx.userId,
    },
  });
});

// ============================================================================
// BASE PROCEDURES
// ============================================================================

export const router = t.router;
export const publicProcedure = t.procedure;
export const protectedProcedure = t.procedure.use(isAuthed);

// ============================================================================
// ROLE-BASED PROCEDURES
// ============================================================================

export const adminProcedure = protectedProcedure.use(requireAdmin);
export const contractorProcedure = protectedProcedure.use(requireContractor);
export const homeownerProcedure = protectedProcedure.use(requireHomeowner);

// ============================================================================
// ORGANIZATION ROLE PROCEDURES
// ============================================================================

export const organizationOwnerProcedure = protectedProcedure.use(requireOrganizationOwner);
export const organizationAdminProcedure = protectedProcedure.use(requireOrganizationAdmin);

// ============================================================================
// PERMISSION-BASED PROCEDURE BUILDERS
// ============================================================================

/**
 * Create a procedure with specific permission requirements
 */
export function createPermissionProcedure(
  resource: ResourceType,
  action: PermissionAction,
  options?: {
    requireOwnership?: boolean;
    allowSameOrganization?: boolean;
    customCheck?: (ctx: any) => Promise<boolean> | boolean;
  }
) {
  return protectedProcedure.use(
    createPermissionMiddleware({
      resource,
      action,
      ...options,
    })
  );
}

/**
 * Create a procedure that requires specific user role(s)
 */
export function createRoleProcedure(requiredRole: UserRole | UserRole[]) {
  return protectedProcedure.use(requireRole(requiredRole));
}

/**
 * Create a procedure that requires specific organization role(s)
 */
export function createOrganizationRoleProcedure(requiredRole: OrganizationRole | OrganizationRole[]) {
  return protectedProcedure.use(requireOrganizationRole(requiredRole));
}

// ============================================================================
// RESOURCE-SPECIFIC PROCEDURES
// ============================================================================

/**
 * Job-related procedures
 */
export const jobProcedures = {
  create: protectedProcedure.use(jobMiddlewares.canCreate),
  read: protectedProcedure.use(jobMiddlewares.canRead),
  update: protectedProcedure.use(jobMiddlewares.canUpdate),
  delete: protectedProcedure.use(jobMiddlewares.canDelete),
  list: protectedProcedure.use(jobMiddlewares.canList),
  manage: protectedProcedure.use(jobMiddlewares.canManage),
};

/**
 * Bid-related procedures
 */
export const bidProcedures = {
  create: protectedProcedure.use(bidMiddlewares.canCreate),
  read: protectedProcedure.use(bidMiddlewares.canRead),
  update: protectedProcedure.use(bidMiddlewares.canUpdate),
  delete: protectedProcedure.use(bidMiddlewares.canDelete),
  list: protectedProcedure.use(bidMiddlewares.canList),
  approve: protectedProcedure.use(bidMiddlewares.canApprove),
  reject: protectedProcedure.use(bidMiddlewares.canReject),
};

/**
 * Property-related procedures
 */
export const propertyProcedures = {
  create: protectedProcedure.use(propertyMiddlewares.canCreate),
  read: protectedProcedure.use(propertyMiddlewares.canRead),
  update: protectedProcedure.use(propertyMiddlewares.canUpdate),
  delete: protectedProcedure.use(propertyMiddlewares.canDelete),
  list: protectedProcedure.use(propertyMiddlewares.canList),
};

/**
 * Organization-related procedures
 */
export const organizationProcedures = {
  create: protectedProcedure.use(organizationMiddlewares.canCreate),
  read: protectedProcedure.use(organizationMiddlewares.canRead),
  update: protectedProcedure.use(organizationMiddlewares.canUpdate),
  delete: protectedProcedure.use(organizationMiddlewares.canDelete),
  list: protectedProcedure.use(organizationMiddlewares.canList),
  manage: protectedProcedure.use(organizationMiddlewares.canManage),
  invite: protectedProcedure.use(organizationMiddlewares.canInvite),
};

/**
 * Message-related procedures
 */
export const messageProcedures = {
  create: protectedProcedure.use(messageMiddlewares.canCreate),
  read: protectedProcedure.use(messageMiddlewares.canRead),
  list: protectedProcedure.use(messageMiddlewares.canList),
};

/**
 * Review-related procedures
 */
export const reviewProcedures = {
  create: protectedProcedure.use(reviewMiddlewares.canCreate),
  read: protectedProcedure.use(reviewMiddlewares.canRead),
  list: protectedProcedure.use(reviewMiddlewares.canList),
};

/**
 * Schedule-related procedures
 */
export const scheduleProcedures = {
  create: protectedProcedure.use(scheduleMiddlewares.canCreate),
  read: protectedProcedure.use(scheduleMiddlewares.canRead),
  update: protectedProcedure.use(scheduleMiddlewares.canUpdate),
  list: protectedProcedure.use(scheduleMiddlewares.canList),
  approve: protectedProcedure.use(scheduleMiddlewares.canApprove),
};

/**
 * Template-related procedures
 */
export const templateProcedures = {
  create: protectedProcedure.use(templateMiddlewares.canCreate),
  read: protectedProcedure.use(templateMiddlewares.canRead),
  update: protectedProcedure.use(templateMiddlewares.canUpdate),
  delete: protectedProcedure.use(templateMiddlewares.canDelete),
  list: protectedProcedure.use(templateMiddlewares.canList),
};

// ============================================================================
// UTILITY PROCEDURES
// ============================================================================

/**
 * Create a CRUD procedure set for a resource
 */
export function createCRUDProcedures(resource: ResourceType) {
  return {
    create: createPermissionProcedure(resource, "create"),
    read: createPermissionProcedure(resource, "read"),
    update: createPermissionProcedure(resource, "update", { requireOwnership: true }),
    delete: createPermissionProcedure(resource, "delete", { requireOwnership: true }),
    list: createPermissionProcedure(resource, "list"),
  };
}

/**
 * Create a read-only procedure set for a resource
 */
export function createReadOnlyProcedures(resource: ResourceType) {
  return {
    read: createPermissionProcedure(resource, "read"),
    list: createPermissionProcedure(resource, "list"),
  };
}

/**
 * Create an admin-only procedure set for a resource
 */
export function createAdminProcedures(resource: ResourceType) {
  return {
    create: adminProcedure,
    read: adminProcedure,
    update: adminProcedure,
    delete: adminProcedure,
    list: adminProcedure,
    manage: adminProcedure,
  };
}

// ============================================================================
// LEGACY COMPATIBILITY
// ============================================================================

/**
 * Legacy admin procedure (for backward compatibility)
 */
export const adminProcedureLegacy = adminProcedure;

/**
 * Legacy protected procedure (for backward compatibility)
 */
export const protectedProcedureLegacy = protectedProcedure;
