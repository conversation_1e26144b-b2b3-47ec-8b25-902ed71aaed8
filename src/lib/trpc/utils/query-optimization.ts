import type { QueryClient, QueryKey, UseQueryOptions } from "@tanstack/react-query";
import type { TRPCQueryOptions } from "@trpc/tanstack-react-query";
import { QUERY_CACHE_STRATEGIES } from "../query-client";

/**
 * Query key factory for consistent cache management
 */
export const queryKeys = {
  // Base keys
  all: ['trpc'] as const,
  
  // Entity-specific keys
  users: () => [...queryKeys.all, 'users'] as const,
  user: (id: string) => [...queryKeys.users(), id] as const,
  userProfile: (id: string) => [...queryKeys.user(id), 'profile'] as const,
  
  organizations: () => [...queryKeys.all, 'organizations'] as const,
  organization: (id: string) => [...queryKeys.organizations(), id] as const,
  organizationMembers: (id: string) => [...queryKeys.organization(id), 'members'] as const,
  
  jobs: () => [...queryKeys.all, 'jobs'] as const,
  job: (id: string) => [...queryKeys.jobs(), id] as const,
  jobBids: (id: string) => [...queryKeys.job(id), 'bids'] as const,
  jobsByProperty: (propertyId: string) => [...queryKeys.jobs(), 'property', propertyId] as const,
  jobsByStatus: (status: string) => [...queryKeys.jobs(), 'status', status] as const,
  
  bids: () => [...queryKeys.all, 'bids'] as const,
  bid: (id: string) => [...queryKeys.bids(), id] as const,
  
  properties: () => [...queryKeys.all, 'properties'] as const,
  property: (id: string) => [...queryKeys.properties(), id] as const,
  
  trades: () => [...queryKeys.all, 'trades'] as const,
  trade: (id: string) => [...queryKeys.trades(), id] as const,
  
  messages: () => [...queryKeys.all, 'messages'] as const,
  messagesByChat: (chatId: string) => [...queryKeys.messages(), 'chat', chatId] as const,
  messagesByBid: (bidId: string) => [...queryKeys.messages(), 'bid', bidId] as const,
  messagesByJob: (jobId: string) => [...queryKeys.messages(), 'job', jobId] as const,
} as const;

/**
 * Query option builders with optimized cache strategies
 */
export const queryOptionBuilders = {
  /**
   * Static data that rarely changes
   */
  static: <T>(baseOptions: TRPCQueryOptions<T>) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.STATIC,
  }),

  /**
   * User-specific data
   */
  user: <T>(baseOptions: TRPCQueryOptions<T>) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.USER,
  }),

  /**
   * Dynamic data that changes frequently
   */
  dynamic: <T>(baseOptions: TRPCQueryOptions<T>) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.DYNAMIC,
  }),

  /**
   * Real-time data
   */
  realtime: <T>(baseOptions: TRPCQueryOptions<T>) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.REALTIME,
  }),

  /**
   * Background data for analytics
   */
  background: <T>(baseOptions: TRPCQueryOptions<T>) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.BACKGROUND,
  }),

  /**
   * Conditional query that only runs when enabled
   */
  conditional: <T>(baseOptions: TRPCQueryOptions<T>, enabled: boolean) => ({
    ...baseOptions,
    enabled,
  }),

  /**
   * Dependent query that depends on other data
   */
  dependent: <T>(baseOptions: TRPCQueryOptions<T>, dependency: unknown) => ({
    ...baseOptions,
    enabled: !!dependency,
  }),
};

/**
 * Cache invalidation utilities
 */
export const cacheInvalidation = {
  /**
   * Invalidate all queries for a specific entity
   */
  invalidateEntity: (queryClient: QueryClient, entityKey: QueryKey) => {
    return queryClient.invalidateQueries({ queryKey: entityKey });
  },

  /**
   * Invalidate related queries when a job is updated
   */
  invalidateJobRelated: (queryClient: QueryClient, jobId: string, propertyId?: string) => {
    const promises = [
      queryClient.invalidateQueries({ queryKey: queryKeys.job(jobId) }),
      queryClient.invalidateQueries({ queryKey: queryKeys.jobBids(jobId) }),
      queryClient.invalidateQueries({ queryKey: queryKeys.jobs() }),
    ];

    if (propertyId) {
      promises.push(
        queryClient.invalidateQueries({ queryKey: queryKeys.jobsByProperty(propertyId) })
      );
    }

    return Promise.all(promises);
  },

  /**
   * Invalidate related queries when a bid is updated
   */
  invalidateBidRelated: (queryClient: QueryClient, bidId: string, jobId?: string) => {
    const promises = [
      queryClient.invalidateQueries({ queryKey: queryKeys.bid(bidId) }),
      queryClient.invalidateQueries({ queryKey: queryKeys.bids() }),
    ];

    if (jobId) {
      promises.push(
        queryClient.invalidateQueries({ queryKey: queryKeys.jobBids(jobId) })
      );
    }

    return Promise.all(promises);
  },

  /**
   * Invalidate organization-related queries
   */
  invalidateOrganizationRelated: (queryClient: QueryClient, organizationId: string) => {
    return Promise.all([
      queryClient.invalidateQueries({ queryKey: queryKeys.organization(organizationId) }),
      queryClient.invalidateQueries({ queryKey: queryKeys.organizationMembers(organizationId) }),
      queryClient.invalidateQueries({ queryKey: queryKeys.organizations() }),
    ]);
  },

  /**
   * Invalidate message-related queries
   */
  invalidateMessageRelated: (queryClient: QueryClient, { bidId, jobId }: { bidId?: string; jobId?: string }) => {
    const promises = [
      queryClient.invalidateQueries({ queryKey: queryKeys.messages() }),
    ];

    if (bidId) {
      promises.push(
        queryClient.invalidateQueries({ queryKey: queryKeys.messagesByBid(bidId) })
      );
    }

    if (jobId) {
      promises.push(
        queryClient.invalidateQueries({ queryKey: queryKeys.messagesByJob(jobId) })
      );
    }

    return Promise.all(promises);
  },
};

/**
 * Background refresh utilities
 */
export const backgroundRefresh = {
  /**
   * Setup background refresh for critical data
   */
  setupCriticalDataRefresh: (queryClient: QueryClient) => {
    // Refresh user data every 5 minutes
    const userRefreshInterval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: queryKeys.users() });
    }, 5 * 60 * 1000);

    // Refresh organization data every 10 minutes
    const orgRefreshInterval = setInterval(() => {
      queryClient.invalidateQueries({ queryKey: queryKeys.organizations() });
    }, 10 * 60 * 1000);

    // Return cleanup function
    return () => {
      clearInterval(userRefreshInterval);
      clearInterval(orgRefreshInterval);
    };
  },

  /**
   * Prefetch related data in the background
   */
  prefetchRelatedData: async (queryClient: QueryClient, trpc: any, context: {
    userId?: string;
    organizationId?: string;
    jobId?: string;
  }) => {
    const promises: Promise<any>[] = [];

    if (context.userId) {
      promises.push(
        queryClient.prefetchQuery(trpc.users.profile.queryOptions({ id: context.userId }))
      );
    }

    if (context.organizationId) {
      promises.push(
        queryClient.prefetchQuery(trpc.contractor.getCrewMembers.queryOptions({ 
          organizationId: context.organizationId 
        }))
      );
    }

    if (context.jobId) {
      promises.push(
        queryClient.prefetchQuery(trpc.bids.listByJob.queryOptions({ jobId: context.jobId }))
      );
    }

    return Promise.allSettled(promises);
  },
};

/**
 * Query state aggregation utilities
 */
export const queryStateUtils = {
  /**
   * Aggregate loading states from multiple queries
   */
  aggregateLoadingStates: (...queries: Array<{ isLoading: boolean }>) => {
    return queries.some(query => query.isLoading);
  },

  /**
   * Aggregate error states from multiple queries
   */
  aggregateErrorStates: (...queries: Array<{ error: Error | null }>) => {
    return queries.find(query => query.error)?.error || null;
  },

  /**
   * Check if all queries have data
   */
  allQueriesHaveData: (...queries: Array<{ data: unknown }>) => {
    return queries.every(query => query.data !== undefined);
  },

  /**
   * Get combined data from multiple queries
   */
  combineQueryData: <T extends Record<string, unknown>>(
    queryMap: { [K in keyof T]: { data: T[K] } }
  ): T | null => {
    const result = {} as T;
    
    for (const [key, query] of Object.entries(queryMap)) {
      if (query.data === undefined) {
        return null;
      }
      result[key as keyof T] = query.data;
    }
    
    return result;
  },
};
