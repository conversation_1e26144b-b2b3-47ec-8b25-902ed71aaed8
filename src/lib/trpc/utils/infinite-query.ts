import type { UseInfiniteQueryOptions } from "@tanstack/react-query";
import type { TRPCInfiniteQueryOptions } from "@trpc/tanstack-react-query";
import { QUERY_CACHE_STRATEGIES } from "../query-client";

/**
 * Cursor-based pagination input type
 */
export type CursorPaginationInput = {
  limit?: number;
  cursor?: string;
};

/**
 * Offset-based pagination input type
 */
export type OffsetPaginationInput = {
  limit?: number;
  offset?: number;
};

/**
 * Paginated response type
 */
export type PaginatedResponse<T> = {
  items: T[];
  nextCursor?: string;
  hasNextPage: boolean;
  totalCount?: number;
};

/**
 * Infinite query builders for different pagination strategies
 */
export const infiniteQueryBuilders = {
  /**
   * Cursor-based infinite query
   */
  cursor: <TInput extends CursorPaginationInput, TOutput extends PaginatedResponse<any>>(
    baseOptions: Omit<TRPCInfiniteQueryOptions<TInput, TOutput>, 'getNextPageParam' | 'initialPageParam'>
  ) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.DYNAMIC,
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage: TOutput) => {
      return lastPage.hasNextPage ? lastPage.nextCursor : undefined;
    },
    getPreviousPageParam: () => undefined, // We don't support previous page navigation
  }),

  /**
   * Offset-based infinite query
   */
  offset: <TInput extends OffsetPaginationInput, TOutput extends { items: any[]; hasNextPage: boolean }>(
    baseOptions: Omit<TRPCInfiniteQueryOptions<TInput, TOutput>, 'getNextPageParam' | 'initialPageParam'>,
    pageSize = 10
  ) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.DYNAMIC,
    initialPageParam: 0,
    getNextPageParam: (lastPage: TOutput, allPages: TOutput[]) => {
      return lastPage.hasNextPage ? allPages.length * pageSize : undefined;
    },
    getPreviousPageParam: (firstPage: TOutput, allPages: TOutput[]) => {
      return allPages.length > 1 ? (allPages.length - 2) * pageSize : undefined;
    },
  }),

  /**
   * Real-time infinite query for chat/messages
   */
  realtime: <TInput extends CursorPaginationInput, TOutput extends PaginatedResponse<any>>(
    baseOptions: Omit<TRPCInfiniteQueryOptions<TInput, TOutput>, 'getNextPageParam' | 'initialPageParam'>
  ) => ({
    ...baseOptions,
    ...QUERY_CACHE_STRATEGIES.REALTIME,
    initialPageParam: undefined as string | undefined,
    getNextPageParam: (lastPage: TOutput) => {
      return lastPage.hasNextPage ? lastPage.nextCursor : undefined;
    },
    refetchInterval: 10 * 1000, // Refetch every 10 seconds for real-time data
  }),
};

/**
 * Infinite query utilities
 */
export const infiniteQueryUtils = {
  /**
   * Flatten all pages into a single array
   */
  flattenInfiniteData: <T>(data: { pages: Array<{ items: T[] }> } | undefined): T[] => {
    if (!data?.pages) return [];
    return data.pages.flatMap(page => page.items);
  },

  /**
   * Get total count from infinite query data
   */
  getTotalCount: (data: { pages: Array<{ totalCount?: number }> } | undefined): number => {
    if (!data?.pages?.[0]?.totalCount) return 0;
    return data.pages[0].totalCount;
  },

  /**
   * Check if infinite query has more pages
   */
  hasNextPage: (data: { pages: Array<{ hasNextPage: boolean }> } | undefined): boolean => {
    if (!data?.pages) return false;
    const lastPage = data.pages[data.pages.length - 1];
    return lastPage?.hasNextPage ?? false;
  },

  /**
   * Get loading state for infinite query
   */
  getInfiniteLoadingState: (query: {
    isLoading: boolean;
    isFetchingNextPage: boolean;
    isFetching: boolean;
  }) => ({
    isInitialLoading: query.isLoading,
    isLoadingMore: query.isFetchingNextPage,
    isRefreshing: query.isFetching && !query.isLoading && !query.isFetchingNextPage,
  }),

  /**
   * Optimistically add item to infinite query data
   */
  optimisticallyAddItem: <T extends { id: string }>(
    queryClient: any,
    queryKey: any[],
    newItem: T,
    position: 'start' | 'end' = 'start'
  ) => {
    queryClient.setQueryData(queryKey, (oldData: any) => {
      if (!oldData?.pages) return oldData;

      const newPages = [...oldData.pages];
      
      if (position === 'start' && newPages[0]) {
        newPages[0] = {
          ...newPages[0],
          items: [newItem, ...newPages[0].items],
        };
      } else if (position === 'end' && newPages[newPages.length - 1]) {
        const lastPageIndex = newPages.length - 1;
        newPages[lastPageIndex] = {
          ...newPages[lastPageIndex],
          items: [...newPages[lastPageIndex].items, newItem],
        };
      }

      return {
        ...oldData,
        pages: newPages,
      };
    });
  },

  /**
   * Optimistically update item in infinite query data
   */
  optimisticallyUpdateItem: <T extends { id: string }>(
    queryClient: any,
    queryKey: any[],
    itemId: string,
    updater: (item: T) => T
  ) => {
    queryClient.setQueryData(queryKey, (oldData: any) => {
      if (!oldData?.pages) return oldData;

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        items: page.items.map((item: T) => 
          item.id === itemId ? updater(item) : item
        ),
      }));

      return {
        ...oldData,
        pages: newPages,
      };
    });
  },

  /**
   * Optimistically remove item from infinite query data
   */
  optimisticallyRemoveItem: <T extends { id: string }>(
    queryClient: any,
    queryKey: any[],
    itemId: string
  ) => {
    queryClient.setQueryData(queryKey, (oldData: any) => {
      if (!oldData?.pages) return oldData;

      const newPages = oldData.pages.map((page: any) => ({
        ...page,
        items: page.items.filter((item: T) => item.id !== itemId),
      }));

      return {
        ...oldData,
        pages: newPages,
      };
    });
  },
};

/**
 * Infinite query hooks factory
 */
export const createInfiniteQueryHooks = <T extends { id: string }>(entityName: string) => {
  return {
    /**
     * Use infinite query with optimistic updates
     */
    useOptimisticInfiniteQuery: (
      queryKey: any[],
      queryFn: any,
      options?: Partial<UseInfiniteQueryOptions>
    ) => {
      // This would be implemented with the actual tRPC hook
      // Return type would include optimistic update methods
      return {
        // Standard infinite query properties
        data: undefined as any,
        isLoading: false,
        isFetchingNextPage: false,
        hasNextPage: false,
        fetchNextPage: () => Promise.resolve(),
        
        // Optimistic update methods
        optimisticallyAdd: (item: T, position: 'start' | 'end' = 'start') => {
          // Implementation would use queryClient from context
        },
        optimisticallyUpdate: (itemId: string, updater: (item: T) => T) => {
          // Implementation would use queryClient from context
        },
        optimisticallyRemove: (itemId: string) => {
          // Implementation would use queryClient from context
        },
      };
    },
  };
};

/**
 * Common infinite query configurations
 */
export const infiniteQueryConfigs = {
  /**
   * Jobs list with cursor pagination
   */
  jobs: {
    pageSize: 20,
    staleTime: QUERY_CACHE_STRATEGIES.DYNAMIC.staleTime,
  },

  /**
   * Messages list with cursor pagination (real-time)
   */
  messages: {
    pageSize: 50,
    staleTime: QUERY_CACHE_STRATEGIES.REALTIME.staleTime,
    refetchInterval: 10 * 1000,
  },

  /**
   * Bids list with offset pagination
   */
  bids: {
    pageSize: 15,
    staleTime: QUERY_CACHE_STRATEGIES.DYNAMIC.staleTime,
  },

  /**
   * Search results with cursor pagination
   */
  search: {
    pageSize: 10,
    staleTime: 30 * 1000, // Short stale time for search results
  },

  /**
   * Notifications with cursor pagination (real-time)
   */
  notifications: {
    pageSize: 25,
    staleTime: 0, // Always fresh
    refetchInterval: 30 * 1000,
  },
} as const;

/**
 * Pagination helpers
 */
export const paginationHelpers = {
  /**
   * Create cursor pagination input
   */
  createCursorInput: (limit = 10, cursor?: string): CursorPaginationInput => ({
    limit,
    cursor,
  }),

  /**
   * Create offset pagination input
   */
  createOffsetInput: (limit = 10, offset = 0): OffsetPaginationInput => ({
    limit,
    offset,
  }),

  /**
   * Calculate offset from page number
   */
  pageToOffset: (page: number, pageSize: number): number => {
    return Math.max(0, (page - 1) * pageSize);
  },

  /**
   * Calculate page number from offset
   */
  offsetToPage: (offset: number, pageSize: number): number => {
    return Math.floor(offset / pageSize) + 1;
  },
};
