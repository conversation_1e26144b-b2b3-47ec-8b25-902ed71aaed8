import { TRPCError } from "@trpc/server";
import { and, eq } from "drizzle-orm";
import { db } from "@/db";
import {
  account,
  address,
  job,
  membership,
  organization,
  property,
} from "@/db/schema";

export async function checkPropertyOwnership(
  userId: string,
  propertyId: string,
) {
  const isPropertyOwner = await db.query.account.findFirst({
    where: and(eq(account.userId, userId), eq(property.id, propertyId)),
  });

  return !!isPropertyOwner;
}

export async function checkJobOwnership(userId: string, jobId: string) {
  const isPropertyOwner = await db.query.account.findFirst({
    where: and(eq(account.userId, userId), eq(job.id, jobId)),
  });

  return !!isPropertyOwner;
}

export async function checkOrganizationMembership(
  userId: string,
  organizationId: string,
  role?: string,
) {
  const member = await db.query.membership.findFirst({
    where: and(
      eq(membership.userId, userId),
      eq(membership.organizationId, organizationId),
      role ? eq(membership.role, role) : undefined,
    ),
  });

  return !!member;
}

export async function getUserOrganization(userId: string) {
  const [result] = await db
    .select()
    .from(organization)
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .innerJoin(address, eq(organization.addressId, address.id))
    .where(eq(membership.userId, userId));

  return {
    ...result?.organization,
    membership: result?.membership,
    address: result?.address,
  };
}

export function requireAuth(condition: boolean, message = "Unauthorized") {
  if (!condition) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message,
    });
  }
}
