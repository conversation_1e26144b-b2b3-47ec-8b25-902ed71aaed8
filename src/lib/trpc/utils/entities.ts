import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { db } from "@/db";
import { bid, chat, job } from "@/db/schema";

export async function findOrCreateChat({
  bidId,
  jobId,
}: {
  bidId?: string;
  jobId?: string;
}) {
  if (!bidId && !jobId) {
    throw new TRPCError({
      code: "BAD_REQUEST",
      message: "Either bidId or jobId must be provided",
    });
  }

  let result = null;

  if (bidId) {
    result = await db.query.chat.findFirst({
      where: eq(chat.bidId, bidId),
    });

    if (!result) {
      [result] = await db
        .insert(chat)
        .values({
          bidId,
        })
        .returning();
    }
  } else if (jobId) {
    result = await db.query.chat.findFirst({
      where: eq(chat.jobId, jobId),
    });

    if (!result) {
      [result] = await db.insert(chat).values({ jobId }).returning();
    }
  }

  return result;
}

export async function getBidWithRelations(bidId: string) {
  const result = await db.query.bid.findFirst({
    where: eq(bid.id, bidId),
    with: {
      job: {
        with: {
          property: {
            with: {
              address: true,
            },
          },
        },
      },
      organization: {
        with: {
          trade: true,
          address: true,
        },
      },
    },
  });

  if (!result) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Bid not found",
    });
  }

  return result;
}

export async function getJobWithRelations(jobId: string) {
  const result = await db.query.job.findFirst({
    where: eq(job.id, jobId),
    with: {
      property: {
        with: {
          address: true,
        },
      },
    },
  });

  if (!result) {
    throw new TRPCError({
      code: "NOT_FOUND",
      message: "Job not found",
    });
  }

  return result;
}
