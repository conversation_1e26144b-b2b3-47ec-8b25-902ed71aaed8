import { and, asc, desc, eq, inArray, sql, count, avg } from "drizzle-orm";
import type { Db } from "@/db";
import { 
  account, 
  address, 
  bid, 
  job, 
  organization, 
  property, 
  trade,
  membership,
  review,
  schedule,
  user
} from "@/db/schema";

/**
 * Optimized Query Implementations
 * 
 * This file contains optimized versions of the most problematic queries
 * found in the tRPC routers. Each function replaces multiple inefficient
 * queries with a single optimized query.
 */

// ============================================================================
// JOBS ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace jobs/listing.ts listForUser query
 * 
 * BEFORE: Multiple queries + N+1 pattern
 * AFTER: Single query with all data
 */
export async function getOptimizedUserJobs(db: Db, userId: string) {
  return db
    .select({
      // Job data
      id: job.id,
      name: job.name,
      budget: job.budget,
      status: job.status,
      createdAt: job.createdAt,
      startsAt: job.startsAt,
      deadline: job.deadline,
      completedAt: job.completedAt,
      
      // Property data
      property: {
        id: property.id,
        name: property.name,
        address: {
          id: address.id,
          street: address.street,
          city: address.city,
          state: address.state,
          zip: address.zip,
        },
      },
      
      // Aggregated bid data
      bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
      acceptedBid: sql<any>`
        json_agg(
          json_build_object(
            'id', ${bid.id},
            'amount', ${bid.amount},
            'status', ${bid.status},
            'organizationName', ${organization.name}
          )
        ) FILTER (WHERE ${bid.status} = 'ACCEPTED')
      `.as("acceptedBid"),
      hasAcceptedBid: sql<boolean>`bool_or(${bid.status} = 'ACCEPTED')`.as("hasAcceptedBid"),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .leftJoin(organization, eq(bid.organizationId, organization.id))
    .where(eq(property.userId, userId))
    .groupBy(job.id, property.id, address.id)
    .orderBy(desc(job.createdAt));
}

/**
 * OPTIMIZED: Replace jobs/listing.ts listPublished query
 * 
 * BEFORE: Simple query but missing optimization for large datasets
 * AFTER: Optimized with pagination and proper indexing
 */
export async function getOptimizedPublishedJobs(
  db: Db, 
  limit = 20, 
  cursor?: string
) {
  let query = db
    .select({
      id: job.id,
      name: job.name,
      budget: job.budget,
      status: job.status,
      createdAt: job.createdAt,
      startsAt: job.startsAt,
      deadline: job.deadline,
      
      property: {
        id: property.id,
        name: property.name,
        address: {
          street: address.street,
          city: address.city,
          state: address.state,
          zip: address.zip,
        },
      },
      
      bidsCount: sql<number>`count(${bid.id})`.as("bidsCount"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),
    })
    .from(job)
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .leftJoin(bid, eq(job.id, bid.jobId))
    .where(eq(job.status, "PUBLISHED"))
    .groupBy(job.id, property.id, address.id);

  if (cursor) {
    query = query.having(sql`${job.createdAt} < ${cursor}`);
  }

  return query
    .orderBy(desc(job.createdAt))
    .limit(limit + 1); // +1 for pagination
}

// ============================================================================
// BIDS ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace bids.ts listForOrganization query
 * 
 * BEFORE: Complex nested relations causing N+1 queries
 * AFTER: Single optimized query with all required data
 */
export async function getOptimizedOrganizationBids(db: Db, organizationId: string) {
  return db
    .select({
      // Bid data
      id: bid.id,
      amount: bid.amount,
      status: bid.status,
      createdAt: bid.createdAt,
      notes: bid.notes,
      
      // Job data
      job: {
        id: job.id,
        name: job.name,
        budget: job.budget,
        status: job.status,
        startsAt: job.startsAt,
        deadline: job.deadline,
        property: {
          name: property.name,
          address: {
            street: address.street,
            city: address.city,
            state: address.state,
          },
        },
      },
      
      // Competition data
      totalBids: sql<number>`
        (SELECT count(*) FROM ${bid} b2 WHERE b2.job_id = ${job.id})
      `.as("totalBids"),
      
      isLowestBid: sql<boolean>`
        ${bid.amount} = (SELECT min(amount) FROM ${bid} b3 WHERE b3.job_id = ${job.id})
      `.as("isLowestBid"),
    })
    .from(bid)
    .innerJoin(job, eq(bid.jobId, job.id))
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(address, eq(property.addressId, address.id))
    .where(eq(bid.organizationId, organizationId))
    .orderBy(desc(bid.createdAt));
}

// ============================================================================
// CONTRACTOR ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace contractor/search.ts search query
 * 
 * BEFORE: ILIKE queries (slow on large datasets)
 * AFTER: Full-text search with ranking
 */
export async function getOptimizedContractorSearch(
  db: Db,
  searchQuery: string,
  excludeIds: string[] = [],
  limit = 10
) {
  const tsQuery = searchQuery.trim().split(' ').join(' & ');
  
  let whereConditions = [
    sql`(
      to_tsvector('english', ${organization.name}) @@ to_tsquery('english', ${tsQuery}) OR
      to_tsvector('english', ${trade.name}) @@ to_tsquery('english', ${tsQuery}) OR
      to_tsvector('english', coalesce(${organization.description}, '')) @@ to_tsquery('english', ${tsQuery})
    )`
  ];

  if (excludeIds.length > 0) {
    whereConditions.push(sql`${organization.id} NOT IN (${sql.join(excludeIds.map(id => sql`${id}`), sql`, `)})`);
  }

  return db
    .select({
      id: organization.id,
      name: organization.name,
      description: organization.description,
      logoUrl: organization.logoUrl,
      email: organization.email,
      phone: organization.phone,
      acceptsQuickHire: organization.acceptsQuickHire,
      
      trade: {
        id: trade.id,
        name: trade.name,
      },
      
      address: {
        street: address.street,
        city: address.city,
        state: address.state,
        zip: address.zip,
      },
      
      // Statistics
      memberCount: sql<number>`count(distinct ${membership.id})`.as("memberCount"),
      completedJobsCount: sql<number>`
        count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')
      `.as("completedJobsCount"),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
      
      // Search ranking
      searchRank: sql<number>`
        ts_rank(
          to_tsvector('english', ${organization.name} || ' ' || coalesce(${organization.description}, '')),
          to_tsquery('english', ${tsQuery})
        )
      `.as("searchRank"),
    })
    .from(organization)
    .leftJoin(trade, eq(organization.tradeId, trade.id))
    .leftJoin(address, eq(organization.addressId, address.id))
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .leftJoin(bid, and(
      eq(organization.id, bid.organizationId),
      eq(bid.status, "ACCEPTED")
    ))
    .leftJoin(job, and(
      eq(bid.jobId, job.id),
      eq(job.status, "COMPLETED")
    ))
    .leftJoin(review, and(
      eq(review.jobId, job.id),
      eq(review.reviewType, "CONTRACTOR_REVIEW")
    ))
    .where(and(...whereConditions))
    .groupBy(organization.id, trade.id, address.id)
    .orderBy(
      desc(sql`avg(${review.rating})`),
      desc(sql`ts_rank(
        to_tsvector('english', ${organization.name} || ' ' || coalesce(${organization.description}, '')),
        to_tsquery('english', ${tsQuery})
      )`),
      asc(organization.name)
    )
    .limit(limit);
}

// ============================================================================
// ADMIN ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace admin.ts getStats multiple queries
 * 
 * BEFORE: 6+ separate count queries
 * AFTER: Single query with all statistics
 */
export async function getOptimizedAdminStats(db: Db) {
  const [stats] = await db
    .select({
      // User statistics
      totalUsers: sql<number>`count(distinct ${account.id})`.as("totalUsers"),
      
      // Job statistics
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      draftJobs: sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'DRAFT')`.as("draftJobs"),
      publishedJobs: sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED')`.as("publishedJobs"),
      awardedJobs: sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'AWARDED')`.as("awardedJobs"),
      completedJobs: sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as("completedJobs"),
      
      // Bid statistics
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids: sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as("acceptedBids"),
      
      // Organization statistics
      totalOrganizations: sql<number>`count(distinct ${organization.id})`.as("totalOrganizations"),
      
      // Financial statistics
      totalJobValue: sql<number>`sum(distinct ${job.budget})`.as("totalJobValue"),
      avgJobBudget: sql<number>`avg(distinct ${job.budget})`.as("avgJobBudget"),
      totalBidValue: sql<number>`sum(${bid.amount})`.as("totalBidValue"),
      avgBidAmount: sql<number>`avg(${bid.amount})`.as("avgBidAmount"),
    })
    .from(account)
    .fullJoin(job, sql`true`)
    .fullJoin(bid, sql`true`)
    .fullJoin(organization, sql`true`);

  return stats;
}

// ============================================================================
// REVIEWS ROUTER OPTIMIZATIONS
// ============================================================================

/**
 * OPTIMIZED: Replace reviews.ts listForOrganizations query
 * 
 * BEFORE: Multiple queries with complex filtering
 * AFTER: Single optimized query with proper joins
 */
export async function getOptimizedOrganizationReviews(
  db: Db,
  organizationIds: string[]
) {
  if (organizationIds.length === 0) return [];

  return db
    .select({
      id: review.id,
      rating: review.rating,
      comment: review.comment,
      reviewType: review.reviewType,
      createdAt: review.createdAt,
      
      job: {
        id: job.id,
        name: job.name,
        completedAt: job.completedAt,
      },
      
      organizationId: bid.organizationId,
      
      // Reviewer info (property owner)
      reviewer: {
        name: user.name,
      },
    })
    .from(review)
    .innerJoin(job, eq(review.jobId, job.id))
    .innerJoin(bid, and(
      eq(bid.jobId, job.id),
      eq(bid.status, "ACCEPTED"),
      inArray(bid.organizationId, organizationIds)
    ))
    .innerJoin(property, eq(job.propertyId, property.id))
    .innerJoin(user, eq(property.userId, user.id))
    .where(and(
      eq(job.status, "COMPLETED"),
      eq(review.reviewType, "CONTRACTOR_REVIEW")
    ))
    .orderBy(desc(review.createdAt));
}

// ============================================================================
// UTILITY FUNCTIONS
// ============================================================================

/**
 * Get organization statistics in a single query
 */
export async function getOrganizationStats(db: Db, organizationId: string) {
  const [stats] = await db
    .select({
      totalJobs: sql<number>`count(distinct ${job.id})`.as("totalJobs"),
      completedJobs: sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'COMPLETED')`.as("completedJobs"),
      activeJobs: sql<number>`count(distinct ${job.id}) filter (where ${job.status} = 'PUBLISHED' and ${bid.status} = 'ACCEPTED')`.as("activeJobs"),
      totalBids: sql<number>`count(distinct ${bid.id})`.as("totalBids"),
      acceptedBids: sql<number>`count(distinct ${bid.id}) filter (where ${bid.status} = 'ACCEPTED')`.as("acceptedBids"),
      avgRating: sql<number>`avg(${review.rating})`.as("avgRating"),
      reviewCount: sql<number>`count(distinct ${review.id})`.as("reviewCount"),
      memberCount: sql<number>`count(distinct ${membership.id})`.as("memberCount"),
    })
    .from(organization)
    .leftJoin(bid, eq(organization.id, bid.organizationId))
    .leftJoin(job, eq(bid.jobId, job.id))
    .leftJoin(review, and(
      eq(review.jobId, job.id),
      eq(review.reviewType, "CONTRACTOR_REVIEW")
    ))
    .leftJoin(membership, eq(organization.id, membership.organizationId))
    .where(eq(organization.id, organizationId))
    .groupBy(organization.id);

  return stats;
}
