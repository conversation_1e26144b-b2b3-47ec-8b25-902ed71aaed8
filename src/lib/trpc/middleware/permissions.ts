import { initTR<PERSON>, TRPCError } from "@trpc/server";
import superjson from "superjson";
import type { Context } from "../core/context";
import type {
  OrganizationRole,
  PermissionAction,
  PermissionContext,
  PermissionMiddlewareOptions,
  ResourceType,
  UserRole,
} from "../types/permissions";
import {
  getUserOrganizationInfo,
  requirePermission,
} from "./permission-utils";

// Initialize tRPC for middleware creation
const t = initTRPC.context<Context>().create({
  transformer: superjson,
});

// ============================================================================
// PERMISSION MIDDLEWARE
// ============================================================================

/**
 * Create permission context from tRPC context
 */
async function createPermissionContext(ctx: Context): Promise<PermissionContext> {
  if (!ctx.userId) {
    throw new TRPCError({
      code: "UNAUTHORIZED",
      message: "Authentication required",
    });
  }

  // Get user's organization info
  const orgInfo = await getUserOrganizationInfo(ctx.userId);

  return {
    userId: ctx.userId,
    userRole: (ctx.role as UserRole) || "homeowner",
    organizationId: orgInfo?.organizationId,
    organizationRole: orgInfo?.role as OrganizationRole,
  };
}

/**
 * Permission middleware factory
 */
export function createPermissionMiddleware(options: PermissionMiddlewareOptions) {
  return t.middleware(async ({ ctx, next, input }) => {
    // Create permission context
    const permissionContext = await createPermissionContext(ctx);

    // Extract resource ID from input if needed
    let resourceId: string | undefined;

    // Try to get resource ID from common input patterns
    if (input && typeof input === 'object') {
      const inputObj = input as Record<string, unknown>;
      resourceId = (inputObj.id || inputObj.resourceId || inputObj[`${options.resource}Id`]) as string;
    }

    // Perform permission check
    if (options.customCheck) {
      const allowed = await options.customCheck(permissionContext);
      if (!allowed) {
        throw new TRPCError({
          code: "FORBIDDEN",
          message: "Custom permission check failed",
        });
      }
    } else {
      await requirePermission(
        permissionContext,
        options.resource,
        options.action,
        resourceId,
        {
          requireOwnership: options.requireOwnership,
          allowSameOrganization: options.allowSameOrganization,
        }
      );
    }

    // Add permission context to the context for use in procedures
    return next({
      ctx: {
        ...ctx,
        permissionContext,
      },
    });
  });
}

/**
 * Role-based middleware
 */
export function requireRole(requiredRole: UserRole | UserRole[]) {
  return t.middleware(async ({ ctx, next }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required",
      });
    }

    const userRole = ctx.role as UserRole;
    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];

    if (!allowedRoles.includes(userRole)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Required role: ${allowedRoles.join(" or ")}. Current role: ${userRole}`,
      });
    }

    return next();
  });
}

/**
 * Organization role middleware
 */
export function requireOrganizationRole(requiredRole: OrganizationRole | OrganizationRole[]) {
  return t.middleware(async ({ ctx, next }) => {
    if (!ctx.userId) {
      throw new TRPCError({
        code: "UNAUTHORIZED",
        message: "Authentication required",
      });
    }

    const orgInfo = await getUserOrganizationInfo(ctx.userId);

    if (!orgInfo) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: "Organization membership required",
      });
    }

    const allowedRoles = Array.isArray(requiredRole) ? requiredRole : [requiredRole];

    if (!allowedRoles.includes(orgInfo.role as OrganizationRole)) {
      throw new TRPCError({
        code: "FORBIDDEN",
        message: `Required organization role: ${allowedRoles.join(" or ")}. Current role: ${orgInfo.role}`,
      });
    }

    return next({
      ctx: {
        ...ctx,
        organizationId: orgInfo.organizationId,
        organizationRole: orgInfo.role as OrganizationRole,
      },
    });
  });
}

/**
 * Resource ownership middleware
 */
export function requireResourceOwnership(resource: ResourceType) {
  return createPermissionMiddleware({
    resource,
    action: "read", // Default action for ownership check
    requireOwnership: true,
  });
}

/**
 * Same organization middleware
 */
export function requireSameOrganization(resource: ResourceType, action: PermissionAction) {
  return createPermissionMiddleware({
    resource,
    action,
    allowSameOrganization: true,
  });
}

/**
 * Admin only middleware
 */
export const requireAdmin = requireRole("admin");

/**
 * Contractor only middleware
 */
export const requireContractor = requireRole("contractor");

/**
 * Homeowner only middleware
 */
export const requireHomeowner = requireRole("homeowner");

/**
 * Organization owner middleware
 */
export const requireOrganizationOwner = requireOrganizationRole("owner");

/**
 * Organization admin or owner middleware
 */
export const requireOrganizationAdmin = requireOrganizationRole(["owner", "admin"]);

// ============================================================================
// SPECIFIC RESOURCE MIDDLEWARES
// ============================================================================

/**
 * Job-specific middlewares
 */
export const jobMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "job", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "job", action: "read" }),
  canUpdate: createPermissionMiddleware({ resource: "job", action: "update", requireOwnership: true }),
  canDelete: createPermissionMiddleware({ resource: "job", action: "delete", requireOwnership: true }),
  canList: createPermissionMiddleware({ resource: "job", action: "list" }),
  canManage: createPermissionMiddleware({ resource: "job", action: "manage", requireOwnership: true }),
};

/**
 * Bid-specific middlewares
 */
export const bidMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "bid", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "bid", action: "read" }),
  canUpdate: createPermissionMiddleware({ resource: "bid", action: "update", requireOwnership: true }),
  canDelete: createPermissionMiddleware({ resource: "bid", action: "delete", requireOwnership: true }),
  canList: createPermissionMiddleware({ resource: "bid", action: "list" }),
  canApprove: createPermissionMiddleware({ resource: "bid", action: "approve" }),
  canReject: createPermissionMiddleware({ resource: "bid", action: "reject" }),
};

/**
 * Property-specific middlewares
 */
export const propertyMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "property", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "property", action: "read" }),
  canUpdate: createPermissionMiddleware({ resource: "property", action: "update", requireOwnership: true }),
  canDelete: createPermissionMiddleware({ resource: "property", action: "delete", requireOwnership: true }),
  canList: createPermissionMiddleware({ resource: "property", action: "list" }),
};

/**
 * Organization-specific middlewares
 */
export const organizationMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "organization", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "organization", action: "read" }),
  canUpdate: createPermissionMiddleware({ resource: "organization", action: "update", requireOwnership: true }),
  canDelete: createPermissionMiddleware({ resource: "organization", action: "delete", requireOwnership: true }),
  canList: createPermissionMiddleware({ resource: "organization", action: "list" }),
  canManage: createPermissionMiddleware({ resource: "organization", action: "manage", requireOwnership: true }),
  canInvite: createPermissionMiddleware({ resource: "user", action: "invite" }),
};

/**
 * Message-specific middlewares
 */
export const messageMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "message", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "message", action: "read" }),
  canList: createPermissionMiddleware({ resource: "message", action: "list" }),
};

/**
 * Review-specific middlewares
 */
export const reviewMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "review", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "review", action: "read" }),
  canList: createPermissionMiddleware({ resource: "review", action: "list" }),
};

/**
 * Schedule-specific middlewares
 */
export const scheduleMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "schedule", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "schedule", action: "read" }),
  canUpdate: createPermissionMiddleware({ resource: "schedule", action: "update" }),
  canList: createPermissionMiddleware({ resource: "schedule", action: "list" }),
  canApprove: createPermissionMiddleware({ resource: "schedule", action: "approve" }),
};

/**
 * Template-specific middlewares
 */
export const templateMiddlewares = {
  canCreate: createPermissionMiddleware({ resource: "template", action: "create" }),
  canRead: createPermissionMiddleware({ resource: "template", action: "read" }),
  canUpdate: createPermissionMiddleware({ resource: "template", action: "update" }),
  canDelete: createPermissionMiddleware({ resource: "template", action: "delete" }),
  canList: createPermissionMiddleware({ resource: "template", action: "list" }),
};
