import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { db } from "@/db";
import {
  user,
  job,
  membership,
  organization,
  property,
  bid,
  message,
  chat,
} from "@/db/schema";
import type {
  PermissionContext,
  PermissionResult,
  ResourceType,
  PermissionAction,
  ResourceOwnership,
  BulkPermissionCheck,
  BulkPermissionResult,
} from "../types/permissions";
import { hasPermission } from "./permission-rules";

// ============================================================================
// PERMISSION CHECKING UTILITIES
// ============================================================================

/**
 * Simple in-memory cache for permissions (in production, use Redis)
 */
const permissionCache = new Map<string, { result: PermissionResult; expiresAt: Date }>();

/**
 * Generate cache key for permission check
 */
function generateCacheKey(
  userId: string,
  resource: ResourceType,
  action: PermissionAction,
  resourceId?: string
): string {
  return `perm:${userId}:${resource}:${action}:${resourceId || 'global'}`;
}

/**
 * Get cached permission result
 */
function getCachedPermission(cacheKey: string): PermissionResult | null {
  const cached = permissionCache.get(cacheKey);
  if (cached && cached.expiresAt > new Date()) {
    return cached.result;
  }
  if (cached) {
    permissionCache.delete(cacheKey);
  }
  return null;
}

/**
 * Cache permission result
 */
function cachePermission(
  cacheKey: string,
  result: PermissionResult,
  ttlSeconds = 300 // 5 minutes default
): void {
  const expiresAt = new Date(Date.now() + ttlSeconds * 1000);
  permissionCache.set(cacheKey, { result, expiresAt });
}

/**
 * Clear permission cache for a user
 */
export function clearUserPermissionCache(userId: string): void {
  for (const [key] of permissionCache) {
    if (key.startsWith(`perm:${userId}:`)) {
      permissionCache.delete(key);
    }
  }
}

/**
 * Get user's organization membership information
 */
export async function getUserOrganizationInfo(userId: string) {
  const membership = await db.query.membership.findFirst({
    where: eq(membership.userId, userId),
    with: {
      organization: true,
    },
  });

  return membership ? {
    organizationId: membership.organizationId,
    role: membership.role,
    organization: membership.organization,
  } : null;
}

/**
 * Check resource ownership
 */
export async function checkResourceOwnership(
  userId: string,
  resource: ResourceType,
  resourceId: string
): Promise<ResourceOwnership | null> {
  switch (resource) {
    case "property": {
      const propertyRecord = await db.query.property.findFirst({
        where: eq(property.id, resourceId),
      });
      return propertyRecord?.userId === userId ? { userId, propertyId: resourceId } : null;
    }

    case "job": {
      const jobRecord = await db.query.job.findFirst({
        where: eq(job.id, resourceId),
        with: { 
          property: true
        },
      });
      return jobRecord?.property?.userId === userId 
        ? { userId, jobId: resourceId, propertyId: jobRecord.propertyId } 
        : null;
    }

    case "bid": {
      const bidRecord = await db.query.bid.findFirst({
        where: eq(bid.id, resourceId),
        with: { 
          organization: { 
            with: { memberships: true } 
          } 
        },
      });
      const isBidOwner = bidRecord?.organization?.memberships?.some(m => m.userId === userId);
      return isBidOwner 
        ? { userId, organizationId: bidRecord.organizationId } 
        : null;
    }

    case "organization": {
      const org = await db.query.organization.findFirst({
        where: eq(organization.id, resourceId),
        with: { memberships: true },
      });
      const membershipRecord = org?.memberships?.find(m => m.userId === userId);
      return membershipRecord 
        ? { userId, organizationId: resourceId } 
        : null;
    }

    case "message": {
      const messageRecord = await db.query.message.findFirst({
        where: eq(message.id, resourceId),
        with: {
          chat: {
            with: {
              job: {
                with: {
                  property: true
                }
              },
              bid: {
                with: {
                  organization: {
                    with: { memberships: true }
                  }
                }
              }
            }
          }
        },
      });

      if (!messageRecord) return null;

      // Check if user owns the job (homeowner)
      if (messageRecord.chat?.job?.property?.userId === userId) {
        return { userId, jobId: messageRecord.chat.jobId };
      }

      // Check if user is part of the bidding organization (contractor)
      const isBidder = messageRecord.chat?.bid?.organization?.memberships?.some(m => m.userId === userId);
      if (isBidder) {
        return { userId, organizationId: messageRecord.chat.bid.organizationId };
      }

      return null;
    }

    default:
      return null;
  }
}

/**
 * Check if user has permission for a specific resource and action
 */
export async function checkPermission(
  context: PermissionContext,
  resource: ResourceType,
  action: PermissionAction,
  resourceId?: string,
  options: {
    requireOwnership?: boolean;
    allowSameOrganization?: boolean;
    cacheKey?: string;
    cacheTTL?: number;
  } = {}
): Promise<PermissionResult> {
  const cacheKey = options.cacheKey || generateCacheKey(context.userId, resource, action, resourceId);
  
  // Check cache first
  const cached = getCachedPermission(cacheKey);
  if (cached) {
    return cached;
  }

  // Check basic role permissions
  const hasRolePermission = hasPermission(context.userRole, resource, action);
  
  if (!hasRolePermission) {
    const result: PermissionResult = {
      allowed: false,
      reason: `Role '${context.userRole}' does not have permission to '${action}' on '${resource}'`,
      requiredRole: context.userRole,
    };
    cachePermission(cacheKey, result, options.cacheTTL);
    return result;
  }

  // If no specific resource ID, and role has permission, allow
  if (!resourceId && !options.requireOwnership) {
    const result: PermissionResult = { allowed: true };
    cachePermission(cacheKey, result, options.cacheTTL);
    return result;
  }

  // Check resource ownership if required
  if (resourceId && (options.requireOwnership || context.userRole !== "admin")) {
    const ownership = await checkResourceOwnership(context.userId, resource, resourceId);
    
    if (!ownership) {
      const result: PermissionResult = {
        allowed: false,
        reason: "User does not own this resource",
      };
      cachePermission(cacheKey, result, options.cacheTTL);
      return result;
    }

    // Check organization-level permissions if applicable
    if (ownership.organizationId && context.organizationRole) {
      const hasOrgPermission = hasPermission(context.organizationRole, resource, action, true);
      if (!hasOrgPermission) {
        const result: PermissionResult = {
          allowed: false,
          reason: `Organization role '${context.organizationRole}' does not have permission to '${action}' on '${resource}'`,
          requiredRole: context.organizationRole,
        };
        cachePermission(cacheKey, result, options.cacheTTL);
        return result;
      }
    }
  }

  // Check same organization access if allowed
  if (options.allowSameOrganization && context.organizationId && resourceId) {
    const ownership = await checkResourceOwnership(context.userId, resource, resourceId);
    if (ownership?.organizationId === context.organizationId) {
      const result: PermissionResult = { allowed: true };
      cachePermission(cacheKey, result, options.cacheTTL);
      return result;
    }
  }

  const result: PermissionResult = { allowed: true };
  cachePermission(cacheKey, result, options.cacheTTL);
  return result;
}

/**
 * Require permission or throw TRPC error
 */
export async function requirePermission(
  context: PermissionContext,
  resource: ResourceType,
  action: PermissionAction,
  resourceId?: string,
  options?: {
    requireOwnership?: boolean;
    allowSameOrganization?: boolean;
    customMessage?: string;
  }
): Promise<void> {
  const result = await checkPermission(context, resource, action, resourceId, options);
  
  if (!result.allowed) {
    throw new TRPCError({
      code: "FORBIDDEN",
      message: options?.customMessage || result.reason || "Permission denied",
    });
  }
}
