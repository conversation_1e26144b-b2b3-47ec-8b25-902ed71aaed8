import type { PermissionRule, ResourceType, PermissionAction } from "../types/permissions";

// ============================================================================
// PERMISSION RULES CONFIGURATION
// ============================================================================

/**
 * Role-based permission rules
 * Defines what each role can do with each resource type
 */
export const PERMISSION_RULES: Record<string, PermissionRule[]> = {
  // ============================================================================
  // ADMIN PERMISSIONS - Full access to everything
  // ============================================================================
  admin: [
    {
      roles: ["admin"],
      permissions: [
        { resource: "job", action: "create" },
        { resource: "job", action: "read" },
        { resource: "job", action: "update" },
        { resource: "job", action: "delete" },
        { resource: "job", action: "list" },
        { resource: "job", action: "manage" },
        { resource: "bid", action: "create" },
        { resource: "bid", action: "read" },
        { resource: "bid", action: "update" },
        { resource: "bid", action: "delete" },
        { resource: "bid", action: "list" },
        { resource: "bid", action: "approve" },
        { resource: "bid", action: "reject" },
        { resource: "property", action: "create" },
        { resource: "property", action: "read" },
        { resource: "property", action: "update" },
        { resource: "property", action: "delete" },
        { resource: "property", action: "list" },
        { resource: "organization", action: "create" },
        { resource: "organization", action: "read" },
        { resource: "organization", action: "update" },
        { resource: "organization", action: "delete" },
        { resource: "organization", action: "list" },
        { resource: "organization", action: "manage" },
        { resource: "user", action: "create" },
        { resource: "user", action: "read" },
        { resource: "user", action: "update" },
        { resource: "user", action: "delete" },
        { resource: "user", action: "list" },
        { resource: "user", action: "manage" },
        { resource: "message", action: "create" },
        { resource: "message", action: "read" },
        { resource: "message", action: "update" },
        { resource: "message", action: "delete" },
        { resource: "message", action: "list" },
        { resource: "review", action: "create" },
        { resource: "review", action: "read" },
        { resource: "review", action: "update" },
        { resource: "review", action: "delete" },
        { resource: "review", action: "list" },
        { resource: "schedule", action: "create" },
        { resource: "schedule", action: "read" },
        { resource: "schedule", action: "update" },
        { resource: "schedule", action: "delete" },
        { resource: "schedule", action: "list" },
        { resource: "template", action: "create" },
        { resource: "template", action: "read" },
        { resource: "template", action: "update" },
        { resource: "template", action: "delete" },
        { resource: "template", action: "list" },
        { resource: "trade", action: "create" },
        { resource: "trade", action: "read" },
        { resource: "trade", action: "update" },
        { resource: "trade", action: "delete" },
        { resource: "trade", action: "list" },
      ],
      description: "Admin has full access to all resources",
    },
  ],

  // ============================================================================
  // HOMEOWNER PERMISSIONS
  // ============================================================================
  homeowner: [
    {
      roles: ["homeowner"],
      permissions: [
        // Jobs - can manage their own jobs
        { resource: "job", action: "create" },
        { resource: "job", action: "read" },
        { resource: "job", action: "update" },
        { resource: "job", action: "delete" },
        { resource: "job", action: "list" },
        // Bids - can read and approve/reject bids on their jobs
        { resource: "bid", action: "read" },
        { resource: "bid", action: "list" },
        { resource: "bid", action: "approve" },
        { resource: "bid", action: "reject" },
        // Properties - can manage their own properties
        { resource: "property", action: "create" },
        { resource: "property", action: "read" },
        { resource: "property", action: "update" },
        { resource: "property", action: "delete" },
        { resource: "property", action: "list" },
        // Messages - can create and read messages for their jobs/bids
        { resource: "message", action: "create" },
        { resource: "message", action: "read" },
        { resource: "message", action: "list" },
        // Reviews - can create reviews for completed jobs
        { resource: "review", action: "create" },
        { resource: "review", action: "read" },
        { resource: "review", action: "list" },
        // Schedules - can read and approve schedules for their jobs
        { resource: "schedule", action: "read" },
        { resource: "schedule", action: "list" },
        { resource: "schedule", action: "approve" },
        // Templates - can read and use templates
        { resource: "template", action: "read" },
        { resource: "template", action: "list" },
        // Trades - can read trade information
        { resource: "trade", action: "read" },
        { resource: "trade", action: "list" },
        // Organizations - can read contractor organizations
        { resource: "organization", action: "read" },
        { resource: "organization", action: "list" },
      ],
      description: "Homeowner can manage their properties and jobs, interact with contractors",
    },
  ],

  // ============================================================================
  // CONTRACTOR PERMISSIONS
  // ============================================================================
  contractor: [
    {
      roles: ["contractor"],
      permissions: [
        // Jobs - can read and list available jobs
        { resource: "job", action: "read" },
        { resource: "job", action: "list" },
        // Bids - can create and manage their own bids
        { resource: "bid", action: "create" },
        { resource: "bid", action: "read" },
        { resource: "bid", action: "update" },
        { resource: "bid", action: "delete" },
        { resource: "bid", action: "list" },
        // Messages - can create and read messages for jobs they bid on
        { resource: "message", action: "create" },
        { resource: "message", action: "read" },
        { resource: "message", action: "list" },
        // Reviews - can create reviews for homeowners and read their own reviews
        { resource: "review", action: "create" },
        { resource: "review", action: "read" },
        { resource: "review", action: "list" },
        // Schedules - can create and manage schedules for awarded jobs
        { resource: "schedule", action: "create" },
        { resource: "schedule", action: "read" },
        { resource: "schedule", action: "update" },
        { resource: "schedule", action: "list" },
        // Templates - can read templates
        { resource: "template", action: "read" },
        { resource: "template", action: "list" },
        // Trades - can read trade information
        { resource: "trade", action: "read" },
        { resource: "trade", action: "list" },
        // Properties - can read property information for jobs they're working on
        { resource: "property", action: "read" },
      ],
      description: "Contractor can bid on jobs, manage their bids and schedules",
    },
  ],
};

/**
 * Organization-level permission rules
 */
export const ORGANIZATION_PERMISSION_RULES: Record<string, PermissionRule[]> = {
  // Organization Owner - can manage everything in their organization
  owner: [
    {
      roles: ["owner"],
      permissions: [
        { resource: "organization", action: "read" },
        { resource: "organization", action: "update" },
        { resource: "organization", action: "delete" },
        { resource: "organization", action: "manage" },
        { resource: "user", action: "invite" },
        { resource: "user", action: "manage" },
        { resource: "bid", action: "create" },
        { resource: "bid", action: "read" },
        { resource: "bid", action: "update" },
        { resource: "bid", action: "delete" },
        { resource: "bid", action: "list" },
      ],
      description: "Organization owner has full control over their organization",
    },
  ],

  // Organization Admin - can manage most things except deleting organization
  admin: [
    {
      roles: ["admin"],
      permissions: [
        { resource: "organization", action: "read" },
        { resource: "organization", action: "update" },
        { resource: "user", action: "invite" },
        { resource: "user", action: "manage" },
        { resource: "bid", action: "create" },
        { resource: "bid", action: "read" },
        { resource: "bid", action: "update" },
        { resource: "bid", action: "delete" },
        { resource: "bid", action: "list" },
      ],
      description: "Organization admin can manage organization and members",
    },
  ],

  // Organization Member - can create bids and manage their own work
  member: [
    {
      roles: ["member"],
      permissions: [
        { resource: "organization", action: "read" },
        { resource: "bid", action: "create" },
        { resource: "bid", action: "read" },
        { resource: "bid", action: "update" },
        { resource: "bid", action: "list" },
      ],
      description: "Organization member can create and manage bids",
    },
  ],

  // Organization Viewer - read-only access
  viewer: [
    {
      roles: ["viewer"],
      permissions: [
        { resource: "organization", action: "read" },
        { resource: "bid", action: "read" },
        { resource: "bid", action: "list" },
      ],
      description: "Organization viewer has read-only access",
    },
  ],
};

/**
 * Get permissions for a specific role
 */
export function getPermissionsForRole(role: string): PermissionRule[] {
  return PERMISSION_RULES[role] || [];
}

/**
 * Get organization permissions for a specific role
 */
export function getOrganizationPermissionsForRole(role: string): PermissionRule[] {
  return ORGANIZATION_PERMISSION_RULES[role] || [];
}

/**
 * Check if a role has permission for a specific resource and action
 */
export function hasPermission(
  role: string,
  resource: ResourceType,
  action: PermissionAction,
  isOrganizationRole = false
): boolean {
  const rules = isOrganizationRole 
    ? getOrganizationPermissionsForRole(role)
    : getPermissionsForRole(role);

  return rules.some(rule =>
    rule.permissions.some(permission =>
      permission.resource === resource && permission.action === action
    )
  );
}
