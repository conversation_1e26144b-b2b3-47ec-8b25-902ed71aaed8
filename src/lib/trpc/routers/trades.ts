import { asc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { organization, trade } from "@/db/schema";
import { adminProcedure, protectedProcedure, router } from "../trpc";

export const tradesRouter = router({
  list: protectedProcedure.query(async () => {
    return db.query.trade.findMany({
      orderBy: [asc(trade.name)],
      with: {
        organizations: true,
      },
      extras: {
        organizationCount: db.$count(organization).as("organizationCount"),
      },
    });
  }),
  create: adminProcedure
    .input(
      z.object({
        name: z.string(),
        availableForQuickHire: z.boolean().default(false),
      }),
    )
    .mutation(async ({ input }) => {
      return db
        .insert(trade)
        .values({
          name: input.name,
          availableForQuickHire: input.availableForQuickHire,
        })
        .returning();
    }),
  one: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      return db.query.trade.findFirst({
        where: eq(trade.id, input.id),
      });
    }),
  update: adminProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        availableForQuickHire: z.boolean(),
      }),
    )
    .mutation(async ({ input }) => {
      return db
        .update(trade)
        .set({
          name: input.name,
          availableForQuickHire: input.availableForQuickHire,
        })
        .where(eq(trade.id, input.id))
        .returning();
    }),
  delete: adminProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input }) => {
      return db.delete(trade).where(eq(trade.id, input.id)).returning();
    }),
  listAvailableForQuickHire: protectedProcedure.query(async () => {
    return db.query.trade.findMany({
      where: eq(trade.availableForQuickHire, true),
      orderBy: [asc(trade.name)],
    });
  }),
});
