import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { address, property } from "@/db/schema";
import { geocodeAddress } from "@/lib/geocoding";
import {
  createPermissionProcedure,
  propertyProcedures,
  router,
} from "../procedures";
import {
  entityInputSchema,
  propertyCreateSchema,
  updateInputSchema,
} from "../schemas";
import { createAddress } from "../utils/addresses";

/**
 * Enhanced Properties Router with Permission System
 *
 * This router demonstrates the new permission-based approach:
 * - Uses pre-built permission procedures for common operations
 * - Leverages standardized schemas from the schemas directory
 * - Implements proper resource ownership checking
 * - Provides clear permission-based access control
 */
export const propertiesEnhancedRouter = router({
  /**
   * List properties - uses permission-based procedure
   * Only returns properties owned by the current user
   */
  list: propertyProcedures.list.query(async ({ ctx }) => {
    const properties = await db
      .select()
      .from(property)
      .innerJoin(address, eq(property.addressId, address.id))
      .where(eq(property.userId, ctx.userId));

    return properties.map((property) => ({
      ...property.property,
      address: property.address,
    }));
  }),

  /**
   * Create property - uses permission-based procedure with standardized schema
   */
  create: propertyProcedures.create
    .input(propertyCreateSchema)
    .mutation(async ({ input, ctx }) => {
      // Geocode the address
      const location = await geocodeAddress(input.address);

      const addressRecord = await createAddress(
        {
          street: input.address.street,
          city: input.address.city,
          state: input.address.state,
          zip: input.address.zip,
          location: location,
        },
        db,
      );

      if (!addressRecord) {
        throw new TRPCError({
          code: "INTERNAL_SERVER_ERROR",
          message: "Failed to create address",
        });
      }

      // Create the property with the address
      const [newProperty] = await db
        .insert(property)
        .values({
          name: input.name,
          imageUrl: input.imageUrl,
          userId: ctx.userId,
          addressId: addressRecord.id,
        })
        .returning();

      return newProperty;
    }),

  /**
   * Update property - uses permission-based procedure with ownership checking
   * The middleware automatically verifies the user owns this property
   */
  update: propertyProcedures.update
    .input(
      updateInputSchema.extend({
        address: z.object({
          street: z.string().min(1, "Street is required"),
          city: z.string().min(1, "City is required"),
          state: z.string().min(1, "State is required"),
          zip: z.string().min(1, "ZIP code is required"),
        }),
      })
    )
    .mutation(async ({ input, ctx }) => {
      // Get the property to update (ownership already verified by middleware)
      const p = await db.query.property.findFirst({
        where: eq(property.id, input.id),
        with: { address: true },
      });

      if (!p) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      // Geocode the address
      const location = await geocodeAddress(input.address);

      // Update the address
      await db
        .update(address)
        .set({
          street: input.address.street,
          city: input.address.city,
          state: input.address.state,
          zip: input.address.zip,
          location: location,
        })
        .where(eq(address.id, p.address.id));

      // Update the property
      const [updatedProperty] = await db
        .update(property)
        .set({
          name: input.name,
        })
        .where(eq(property.id, input.id))
        .returning();

      return updatedProperty;
    }),

  /**
   * Delete property - uses permission-based procedure with ownership checking
   */
  delete: propertyProcedures.delete
    .input(entityInputSchema)
    .mutation(async ({ input }) => {
      // Ownership already verified by middleware
      return await db.delete(property).where(eq(property.id, input.id));
    }),

  /**
   * Get single property - uses permission-based procedure with ownership checking
   */
  one: propertyProcedures.read
    .input(entityInputSchema)
    .query(async ({ input, ctx }) => {
      // Ownership already verified by middleware
      const [result] = await db
        .select()
        .from(property)
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(property.id, input.id));

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      return {
        ...result.property,
        address: result.address,
      };
    }),

  /**
   * Example of custom permission procedure
   * This allows homeowners to share property info with contractors in their organization
   */
  getForOrganization: createPermissionProcedure("property", "read", {
    allowSameOrganization: true,
  })
    .input(entityInputSchema)
    .query(async ({ input }) => {
      const [result] = await db
        .select()
        .from(property)
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(property.id, input.id));

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Property not found",
        });
      }

      return {
        ...result.property,
        address: result.address,
      };
    }),

  /**
   * Example of admin-only procedure
   * Only admins can access all properties
   */
  adminList: createPermissionProcedure("property", "list", {
    customCheck: async (ctx) => ctx.userRole === "admin",
  }).query(async () => {
    const properties = await db
      .select()
      .from(property)
      .innerJoin(address, eq(property.addressId, address.id));

    return properties.map((property) => ({
      ...property.property,
      address: property.address,
    }));
  }),
});

/**
 * Usage Examples:
 *
 * 1. Basic CRUD operations automatically have permission checking:
 *    - list: Only returns user's properties
 *    - create: Only authenticated users can create
 *    - update/delete: Only property owners can modify
 *    - read: Only property owners can view details
 *
 * 2. Custom permissions:
 *    - getForOrganization: Allows organization members to view
 *    - adminList: Only admins can see all properties
 *
 * 3. Standardized schemas:
 *    - Uses propertyCreateSchema for consistent validation
 *    - Uses entityInputSchema for ID-based operations
 *    - Uses updateInputSchema for update operations
 *
 * 4. Automatic error handling:
 *    - Permission denied errors are thrown automatically
 *    - Ownership verification happens in middleware
 *    - No need for manual permission checks in procedures
 */
