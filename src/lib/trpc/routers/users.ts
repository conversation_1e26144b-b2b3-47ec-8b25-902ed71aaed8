import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { account, pushSubscription, user } from "@/db/schema";
import { authClient } from "@/lib/auth-client";
import { defaultUserSettings } from "@/types/user-settings";
import { protectedProcedure, router } from "../trpc";

// Define Zod schema for user settings
const notificationSettingsSchema = z
  .object({
    email: z
      .object({
        marketing: z.boolean().optional(),
        jobUpdates: z.boolean().optional(),
        messages: z.boolean().optional(),
        bids: z.boolean().optional(),
      })
      .optional(),
    push: z
      .object({
        enabled: z.boolean().optional(),
        jobUpdates: z.boolean().optional(),
        messages: z.boolean().optional(),
        bids: z.boolean().optional(),
      })
      .optional(),
  })
  .optional();

const userSettingsSchema = z.object({
  notifications: notificationSettingsSchema,
  theme: z.enum(["light", "dark", "system"]).optional(),
  display: z
    .object({
      compactView: z.boolean().optional(),
      showTutorials: z.boolean().optional(),
    })
    .optional(),
});

export const usersRouter = router({
  getSettings: protectedProcedure.query(async ({ ctx }) => {
    const userId = ctx.userId;

    const userData = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        settings: true,
      },
    });

    if (!userData) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "User not found",
      });
    }

    // Merge with default settings to ensure all fields exist
    return {
      ...defaultUserSettings,
      ...userData.settings,
    };
  }),

  updateSettings: protectedProcedure
    .input(userSettingsSchema)
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.userId;

      const userData = await db.query.user.findFirst({
        where: eq(user.id, userId),
        columns: {
          settings: true,
        },
      });

      if (!userData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "User not found",
        });
      }

      // Merge existing settings with new settings
      const updatedSettings = {
        ...userData.settings,
        ...input,
      };

      // Update user settings
      const [updatedUser] = await db
        .update(user)
        .set({
          settings: updatedSettings,
          updatedAt: new Date(),
        })
        .where(eq(user.id, userId))
        .returning();

      return updatedUser;
    }),

  savePushSubscription: protectedProcedure
    .input(
      z.object({
        subscription: z.object({
          endpoint: z.string(),
          expirationTime: z.number().nullable().optional(),
          keys: z.object({
            p256dh: z.string(),
            auth: z.string(),
          }),
        }),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      const userId = ctx.userId;

      // Get the user's account
      const userAccount = await db.query.account.findFirst({
        where: eq(account.userId, userId),
      });

      if (!userAccount) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Account not found",
        });
      }

      // Store subscription in database
      await db
        .insert(pushSubscription)
        .values({
          endpoint: input.subscription.endpoint,
          p256dh: input.subscription.keys.p256dh,
          auth: input.subscription.keys.auth,
          accountId: userAccount.id,
        })
        .onConflictDoUpdate({
          target: pushSubscription.endpoint,
          set: {
            p256dh: input.subscription.keys.p256dh,
            auth: input.subscription.keys.auth,
            accountId: userAccount.id,
          },
        });

      // Update user settings to enable push notifications
      const userData = await db.query.user.findFirst({
        where: eq(user.id, userId),
        columns: {
          settings: true,
        },
      });

      if (userData) {
        const currentSettings = userData.settings || {};
        const updatedSettings = {
          ...currentSettings,
          notifications: {
            ...currentSettings.notifications,
            push: {
              ...currentSettings.notifications?.push,
              enabled: true,
            },
          },
        };

        await db
          .update(user)
          .set({
            settings: updatedSettings,
            updatedAt: new Date(),
          })
          .where(eq(user.id, userId));
      }

      return { success: true };
    }),

  removePushSubscription: protectedProcedure.mutation(async ({ ctx }) => {
    const userId = ctx.userId;

    // Get the user's account
    const userAccount = await db.query.account.findFirst({
      where: eq(account.userId, userId),
    });

    if (!userAccount) {
      throw new TRPCError({
        code: "NOT_FOUND",
        message: "Account not found",
      });
    }

    // Remove all subscriptions for this account
    await db
      .delete(pushSubscription)
      .where(eq(pushSubscription.accountId, userAccount.id));

    // Update user settings to disable push notifications
    const userData = await db.query.user.findFirst({
      where: eq(user.id, userId),
      columns: {
        settings: true,
      },
    });

    if (userData) {
      const currentSettings = userData.settings || {};
      const updatedSettings = {
        ...currentSettings,
        notifications: {
          ...currentSettings.notifications,
          push: {
            ...currentSettings.notifications?.push,
            enabled: false,
          },
        },
      };

      await db
        .update(user)
        .set({
          settings: updatedSettings,
          updatedAt: new Date(),
        })
        .where(eq(user.id, userId));
    }

    return { success: true };
  }),
  updateProfile: protectedProcedure
    .input(
      z.object({
        name: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      await authClient.updateUser({
        name: input.name,
      });
    }),
  getById: protectedProcedure
    .input(z.object({ id: z.string() }))
    .query(async ({ input }) => {
      return await db.query.user.findFirst({
        where: eq(user.id, input.id),
      });
    }),
});
