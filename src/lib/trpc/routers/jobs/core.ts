import { TRPCError } from "@trpc/server";
import { asc, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import {
  address,
  bid,
  job,
  jobImage,
  organization,
  property,
  schedule,
  task,
} from "@/db/schema";
import { protectedProcedure } from "../../trpc";
import { checkJobOwnership, requireAuth } from "../../utils/permissions";

export const coreJobsRouter = {
  list: protectedProcedure
    .input(z.object({ limit: z.number().optional() }).optional())
    .query(async ({ input }) => {
      const result = await db.query.job.findMany({
        orderBy: [asc(job.name)],
        with: {
          property: true,
          bids: {
            with: {
              organization: true,
            },
          },
        },
        limit: input?.limit,
        extras: {
          bidsCount: db.$count(bid, eq(bid.jobId, job.id)).as("bidsCount"),
        },
      });

      return result;
    }),
  getById: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string().optional(),
      }),
    )
    .query(async ({ input }) => {
      const [jobData] = await db
        .select()
        .from(job)
        .innerJoin(property, eq(job.propertyId, property.id))
        .innerJoin(address, eq(property.addressId, address.id))
        .where(eq(job.id, input.id))
        .limit(1);

      const bids = await db
        .select()
        .from(bid)
        .innerJoin(organization, eq(bid.organizationId, organization.id))
        .where(eq(bid.jobId, input.id));

      const schedules = await db
        .select()
        .from(schedule)
        .where(eq(schedule.jobId, input.id));

      const images = await db
        .select()
        .from(jobImage)
        .where(eq(jobImage.jobId, input.id));

      const tasks = await db
        .select()
        .from(task)
        .where(eq(task.jobId, input.id));

      if (!jobData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      return {
        ...jobData.job,
        property: {
          ...jobData.property,
          address: jobData.address,
        },
        bids: bids.map((bid) => ({
          ...bid.bid,
          organization: bid.organization,
        })),
        schedules: schedules,
        images: images,
        tasks: tasks,
      };
    }),

  update: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        name: z.string(),
        propertyId: z.string(),
        images: z
          .object({
            url: z.string(),
            description: z.string().optional().nullable(),
          })
          .array()
          .optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to update this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to update this job");

      // First, delete existing images if we're updating them
      if (input.images) {
        await db.delete(jobImage).where(eq(jobImage.jobId, input.id));
      }

      // Update the job
      const [result] = await db
        .update(job)
        .set({
          name: input.name,
          propertyId: input.propertyId,
        })
        .where(eq(job.id, input.id))
        .returning();

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Add images if they exist
      if (input.images && input.images.length > 0) {
        await db.insert(jobImage).values(
          input.images.map((image) => ({
            jobId: input.id,
            url: image.url,
            description: image.description,
          })),
        );
      }

      return result;
    }),

  delete: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to delete this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to delete this job");

      // Get the job before deletion
      const result = await db.query.job.findFirst({
        where: eq(job.id, input.id),
      });

      if (!result) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Delete the job
      await db.delete(job).where(eq(job.id, input.id));

      return result;
    }),

  publish: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to publish this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to publish this job");

      // Update the job status to PUBLISHED
      const [result] = await db
        .update(job)
        .set({ status: "PUBLISHED" })
        .where(eq(job.id, input.id))
        .returning();

      return result;
    }),

  cancel: protectedProcedure
    .input(z.object({ id: z.string() }))
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to cancel this job
      const isOwner = await checkJobOwnership(ctx.userId, input.id);
      requireAuth(isOwner, "You don't have permission to cancel this job");

      // Check if job is in a state that can be cancelled
      const jobData = await db.query.job.findFirst({
        where: eq(job.id, input.id),
      });

      if (!jobData) {
        throw new TRPCError({
          code: "NOT_FOUND",
          message: "Job not found",
        });
      }

      // Only allow cancellation of DRAFT or PUBLISHED jobs
      if (jobData.status !== "DRAFT" && jobData.status !== "PUBLISHED") {
        throw new TRPCError({
          code: "BAD_REQUEST",
          message: "Only draft or published projects can be cancelled",
        });
      }

      // Update the job status to CANCELLED
      const [result] = await db
        .update(job)
        .set({ status: "CANCELED" })
        .where(eq(job.id, input.id))
        .returning();

      return result;
    }),
};
