import { TRPCError } from "@trpc/server";
import { eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { jobTemplate, templateTask } from "@/db/schema";
import { adminProcedure, protectedProcedure, router } from "../trpc";

export const templatesRouter = router({
  list: protectedProcedure.query(async () => {
    return db.query.jobTemplate.findMany({
      with: {
        tasks: {
          with: {
            trade: true,
          },
        },
      },
    });
  }),

  create: adminProcedure
    .input(
      z.object({
        name: z.string().min(1, "Name is required"),
        description: z.string().min(1, "Description is required"),
        budget: z.number().min(1, "Budget must be greater than 0"),
        tasks: z.array(
          z.object({
            name: z.string().min(1, "Task name is required"),
            tradeId: z.string().min(1, "Trade is required"),
          })
        ).min(1, "At least one task is required"),
        estimatedDuration: z.number().min(1, "Duration must be at least 1 day").optional(),
      }),
    )
    .mutation(async ({ input }) => {
      // First create the template
      const [newTemplate] = await db
        .insert(jobTemplate)
        .values({
          name: input.name,
          description: input.description,
          budget: input.budget,
          estimatedDuration: input.estimatedDuration || 7,
        })
        .returning();

      if (!newTemplate) {
        throw new TRPCError({ code: "INTERNAL_SERVER_ERROR" });
      }

      // Then create the tasks
      if (input.tasks.length > 0) {
        await db.insert(templateTask).values(
          input.tasks.map((task) => ({
            name: task.name,
            tradeId: task.tradeId,
            templateId: newTemplate.id,
          })),
        );
      }

      return newTemplate;
    }),

  one: protectedProcedure
    .input(z.object({ id: z.string().min(1, "ID is required") }))
    .query(async ({ input }) => {
      const templateData = await db.query.jobTemplate.findFirst({
        where: eq(jobTemplate.id, input.id),
        with: {
          tasks: {
            with: {
              trade: true,
            },
          },
        },
      });
      return templateData;
    }),

  delete: adminProcedure
    .input(z.object({ id: z.string().min(1, "ID is required") }))
    .mutation(async ({ input }) => {
      const deletedTemplate = await db
        .delete(jobTemplate)
        .where(eq(jobTemplate.id, input.id));
      return deletedTemplate;
    }),
});
