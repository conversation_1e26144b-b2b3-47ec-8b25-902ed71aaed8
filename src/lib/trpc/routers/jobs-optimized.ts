import { TRPCError } from "@trpc/server";
import { and, desc, eq, inArray } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { job, property } from "@/db/schema";
import { protectedProcedure, router } from "../trpc";
import { 
  getOptimizedUserJobs,
  getOptimizedPublishedJobs,
} from "../utils/optimized-queries";
import { 
  withQueryPerformanceMonitoring,
  withQueryCache,
} from "../utils/query-performance";
import { 
  batchLoadJobsWithRelations,
  buildCursorPaginationQuery,
} from "../utils/query-optimizations";

/**
 * OPTIMIZED Jobs Router
 * 
 * This is an example of how to refactor the jobs router using the optimization utilities.
 * It demonstrates:
 * 1. Replacing N+1 queries with batch queries
 * 2. Adding query performance monitoring
 * 3. Implementing query result caching
 * 4. Using cursor-based pagination
 * 5. Optimizing complex joins
 */

export const jobsOptimizedRouter = router({
  /**
   * OPTIMIZED: List user's jobs
   * 
   * BEFORE: Multiple queries + N+1 pattern in jobs/listing.ts
   * AFTER: Single optimized query with all data
   */
  listForUser: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(100).default(20),
        cursor: z.string().optional(),
        includeCompleted: z.boolean().default(true),
      }).optional()
    )
    .query(async ({ input, ctx }) => {
      const { limit = 20, cursor, includeCompleted = true } = input || {};
      
      return withQueryPerformanceMonitoring(
        'jobs.listForUser',
        async () => {
          const cacheKey = `user-jobs:${ctx.userId}:${limit}:${cursor}:${includeCompleted}`;
          
          return withQueryCache(
            cacheKey,
            () => getOptimizedUserJobs(db, ctx.userId),
            60000 // 1 minute cache
          );
        }
      );
    }),

  /**
   * OPTIMIZED: List published jobs with pagination
   * 
   * BEFORE: Simple query without optimization
   * AFTER: Cursor-based pagination with caching
   */
  listPublished: protectedProcedure
    .input(
      z.object({
        limit: z.number().min(1).max(50).default(20),
        cursor: z.string().optional(),
      }).optional()
    )
    .query(async ({ input }) => {
      const { limit = 20, cursor } = input || {};
      
      return withQueryPerformanceMonitoring(
        'jobs.listPublished',
        async () => {
          const cacheKey = `published-jobs:${limit}:${cursor || 'first'}`;
          
          return withQueryCache(
            cacheKey,
            () => getOptimizedPublishedJobs(db, limit, cursor),
            30000 // 30 seconds cache for published jobs
          );
        }
      );
    }),

  /**
   * OPTIMIZED: Get job by ID with all relations
   * 
   * BEFORE: Multiple separate queries for relations
   * AFTER: Single batch query with all data
   */
  getById: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        includeReviews: z.boolean().default(false),
        includeSchedules: z.boolean().default(false),
      })
    )
    .query(async ({ input, ctx }) => {
      const { id, includeReviews, includeSchedules } = input;
      
      return withQueryPerformanceMonitoring(
        'jobs.getById',
        async () => {
          const cacheKey = `job:${id}:${includeReviews}:${includeSchedules}`;
          
          return withQueryCache(
            cacheKey,
            async () => {
              const [jobData] = await batchLoadJobsWithRelations(
                db,
                [id],
                {
                  includeBids: true,
                  includeProperty: true,
                  includeReviews,
                  includeSchedules,
                }
              );

              if (!jobData) {
                throw new TRPCError({
                  code: "NOT_FOUND",
                  message: "Job not found",
                });
              }

              // Check permissions
              const hasAccess = 
                jobData.property.userId === ctx.userId || // Property owner
                jobData.bids.some(bid => 
                  bid.organization.memberships?.some(m => m.userId === ctx.userId)
                ); // Bidder

              if (!hasAccess) {
                throw new TRPCError({
                  code: "FORBIDDEN",
                  message: "You don't have access to this job",
                });
              }

              return jobData;
            },
            120000 // 2 minutes cache
          );
        }
      );
    }),

  /**
   * OPTIMIZED: Batch get multiple jobs
   * 
   * NEW: Efficient batch loading for multiple jobs
   */
  getBatch: protectedProcedure
    .input(
      z.object({
        ids: z.array(z.string()).min(1).max(50),
        includeReviews: z.boolean().default(false),
        includeSchedules: z.boolean().default(false),
      })
    )
    .query(async ({ input, ctx }) => {
      const { ids, includeReviews, includeSchedules } = input;
      
      return withQueryPerformanceMonitoring(
        'jobs.getBatch',
        async () => {
          const jobs = await batchLoadJobsWithRelations(
            db,
            ids,
            {
              includeBids: true,
              includeProperty: true,
              includeReviews,
              includeSchedules,
            }
          );

          // Filter jobs based on user permissions
          return jobs.filter(job => {
            const hasAccess = 
              job.property.userId === ctx.userId || // Property owner
              job.bids.some(bid => 
                bid.organization.memberships?.some(m => m.userId === ctx.userId)
              ); // Bidder

            return hasAccess;
          });
        }
      );
    }),

  /**
   * OPTIMIZED: Get jobs for property with history
   * 
   * BEFORE: Complex query with multiple joins
   * AFTER: Optimized single query
   */
  listForProperty: protectedProcedure
    .input(
      z.object({
        propertyId: z.string(),
        includeCompleted: z.boolean().default(true),
        limit: z.number().min(1).max(100).default(50),
      })
    )
    .query(async ({ input, ctx }) => {
      const { propertyId, includeCompleted, limit } = input;
      
      return withQueryPerformanceMonitoring(
        'jobs.listForProperty',
        async () => {
          // First verify property ownership
          const propertyOwner = await db.query.property.findFirst({
            where: eq(property.id, propertyId),
            columns: { userId: true },
          });

          if (!propertyOwner || propertyOwner.userId !== ctx.userId) {
            throw new TRPCError({
              code: "FORBIDDEN",
              message: "You don't have access to this property",
            });
          }

          const cacheKey = `property-jobs:${propertyId}:${includeCompleted}:${limit}`;
          
          return withQueryCache(
            cacheKey,
            async () => {
              let statusFilter = includeCompleted 
                ? undefined 
                : inArray(job.status, ["DRAFT", "PUBLISHED", "AWARDED"]);

              const jobs = await db.query.job.findMany({
                where: and(
                  eq(job.propertyId, propertyId),
                  statusFilter
                ),
                with: {
                  bids: {
                    with: {
                      organization: {
                        with: {
                          trade: true,
                        },
                      },
                    },
                  },
                  schedules: true,
                },
                orderBy: [desc(job.createdAt)],
                limit,
              });

              return jobs;
            },
            60000 // 1 minute cache
          );
        }
      );
    }),

  /**
   * OPTIMIZED: Get job statistics
   * 
   * NEW: Efficient statistics calculation
   */
  getStats: protectedProcedure
    .input(
      z.object({
        jobId: z.string(),
      })
    )
    .query(async ({ input, ctx }) => {
      const { jobId } = input;
      
      return withQueryPerformanceMonitoring(
        'jobs.getStats',
        async () => {
          const cacheKey = `job-stats:${jobId}`;
          
          return withQueryCache(
            cacheKey,
            async () => {
              // Verify access to job
              const jobData = await db.query.job.findFirst({
                where: eq(job.id, jobId),
                with: {
                  property: {
                    columns: { userId: true },
                  },
                  bids: {
                    with: {
                      organization: {
                        with: {
                          memberships: {
                            columns: { userId: true },
                          },
                        },
                      },
                    },
                  },
                },
              });

              if (!jobData) {
                throw new TRPCError({
                  code: "NOT_FOUND",
                  message: "Job not found",
                });
              }

              const hasAccess = 
                jobData.property.userId === ctx.userId ||
                jobData.bids.some(bid => 
                  bid.organization.memberships.some(m => m.userId === ctx.userId)
                );

              if (!hasAccess) {
                throw new TRPCError({
                  code: "FORBIDDEN",
                  message: "You don't have access to this job",
                });
              }

              // Calculate statistics
              const bids = jobData.bids;
              const totalBids = bids.length;
              const acceptedBid = bids.find(bid => bid.status === "ACCEPTED");
              const avgBidAmount = totalBids > 0 
                ? bids.reduce((sum, bid) => sum + bid.amount, 0) / totalBids 
                : 0;
              const lowestBid = totalBids > 0 
                ? Math.min(...bids.map(bid => bid.amount)) 
                : 0;
              const highestBid = totalBids > 0 
                ? Math.max(...bids.map(bid => bid.amount)) 
                : 0;

              return {
                totalBids,
                avgBidAmount,
                lowestBid,
                highestBid,
                acceptedBidAmount: acceptedBid?.amount || null,
                budgetVariance: jobData.budget - avgBidAmount,
                competitionLevel: totalBids > 5 ? 'high' : totalBids > 2 ? 'medium' : 'low',
              };
            },
            300000 // 5 minutes cache for stats
          );
        }
      );
    }),
});
