import { and, eq } from "drizzle-orm";
import z from "zod";
import { db } from "@/db";
import { account, job, property } from "@/db/schema";
import { protectedProcedure, publicProcedure, router } from "../trpc";

export const accountsRouter = router({
  findForUser: publicProcedure
    .input(z.object({ userId: z.string() }))
    .query(async ({ input }) => {
      return await db.query.account.findFirst({
        where: eq(account.userId, input.userId),
      });
    }),
  getStats: protectedProcedure.query(async ({ ctx }) => {
    const totalJobs = await db.$count(
      job,
      eq(
        job.propertyId,
        db
          .select({ id: property.id })
          .from(property)
          .where(eq(property.userId, ctx.userId)),
      ),
    );

    const activeJobs = await db.$count(
      job,
      and(
        eq(job.status, "PUBLISHED"),
        eq(
          job.propertyId,
          db
            .select({ id: property.id })
            .from(property)
            .where(eq(property.userId, ctx.userId)),
        ),
      ),
    );

    const completedJobs = await db.$count(
      job,
      and(
        eq(job.status, "COMPLETED"),
        eq(
          job.propertyId,
          db
            .select({ id: property.id })
            .from(property)
            .where(eq(property.userId, ctx.userId)),
        ),
      ),
    );

    return {
      totalJobs,
      activeJobs,
      completedJobs,
    };
  }),
});
