import { and, count, eq } from "drizzle-orm";
import { db } from "@/db";
import { bid, job } from "@/db/schema";
import { protectedProcedure } from "@/lib/trpc/trpc";
import { getUserOrganization } from "../../utils/permissions";

export const statsRouter = {
  getStats: protectedProcedure.query(async ({ ctx }) => {
    // Find the user's organization

    const userOrg = await getUserOrganization(ctx.userId);

    const organizationId = userOrg?.id;

    if (!organizationId) {
      return {
        totalBids: 0,
        activeJobs: 0,
        completedJobs: 0,
      };
    }

    // Count total bids by this organization
    const totalBids = await db.$count(
      bid,
      eq(bid.organizationId, organizationId),
    );

    // Count active jobs
    const [activeJobs] = await db
      .select({ count: count() })
      .from(job)
      .leftJoin(bid, eq(job.id, bid.jobId))
      .where(
        and(
          eq(job.status, "PUBLISHED"),
          eq(bid.organizationId, organizationId),
          eq(bid.status, "ACCEPTED"),
        ),
      );

    // Count completed jobs
    const [completedJobs] = await db
      .select({ count: count() })
      .from(job)
      .leftJoin(bid, eq(job.id, bid.jobId))
      .where(
        and(
          eq(job.status, "COMPLETED"),
          eq(bid.organizationId, organizationId),
          eq(bid.status, "ACCEPTED"),
        ),
      );

    return {
      totalBids,
      activeJobs: activeJobs?.count || 0,
      completedJobs: completedJobs?.count || 0,
    };
  }),
};
