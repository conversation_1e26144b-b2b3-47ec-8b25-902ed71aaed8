import { and, eq } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { collaboration, membership } from "@/db/schema";
import { protectedProcedure } from "@/lib/trpc/trpc";

export const membersRouter = {
  getMembers: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      const members = await db.query.membership.findMany({
        where: eq(membership.organizationId, input.organizationId),
        with: {
          organization: true,
        },
      });
      return members;
    }),

  getCrewMembers: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      return db.query.collaboration.findMany({
        where: eq(collaboration.primaryOrgId, input.organizationId),
        with: {
          crewMember: {
            with: {
              trade: true,
            },
          },
        },
      });
    }),

  addCrewMember: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        crewMemberId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return db
        .insert(collaboration)
        .values({
          primaryOrgId: input.organizationId,
          crewMemberId: input.crewMemberId,
        })
        .returning();
    }),

  removeCrewMember: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        crewMemberId: z.string(),
      }),
    )
    .mutation(async ({ input }) => {
      return db
        .delete(collaboration)
        .where(
          and(
            eq(collaboration.primaryOrgId, input.organizationId),
            eq(collaboration.crewMemberId, input.crewMemberId),
          ),
        );
    }),
};
