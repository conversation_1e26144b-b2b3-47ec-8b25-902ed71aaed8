import { asc, eq, type InferInsertModel } from "drizzle-orm";
import { z } from "zod";
import { db } from "@/db";
import { service } from "@/db/schema";
import { protectedProcedure } from "@/lib/trpc/trpc";
import {
  checkOrganizationMembership,
  requireAuth,
} from "../../utils/permissions";

export const servicesRouter = {
  getServices: protectedProcedure
    .input(z.object({ organizationId: z.string() }))
    .query(async ({ input }) => {
      return db.query.service.findMany({
        where: eq(service.organizationId, input.organizationId),
        orderBy: [asc(service.name)],
      });
    }),

  createService: protectedProcedure
    .input(
      z.object({
        organizationId: z.string(),
        name: z.string(),
        description: z.string(),
        price: z.number(),
        duration: z.number(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to add services to this organization
      const isMember = await checkOrganizationMembership(
        ctx.userId,
        input.organizationId,
        "owner",
      );
      requireAuth(
        isMember,
        "You don't have permission to add services to this organization",
      );

      const query: InferInsertModel<typeof service> = {
        organizationId: input.organizationId,
        name: input.name,
        description: input.description,
        price: input.price,
        duration: input.duration,
      };

      return db.insert(service).values(query);
    }),

  updateService: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
        name: z.string(),
        description: z.string().optional(),
        price: z.number(),
        duration: z.number().optional(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to update services for this organization
      const isMember = await checkOrganizationMembership(
        ctx.userId,
        input.organizationId,
        "owner",
      );
      requireAuth(
        isMember,
        "You don't have permission to update services for this organization",
      );

      const [result] = await db
        .update(service)
        .set({
          name: input.name,
          description: input.description || "",
          price: input.price,
          duration: input.duration,
        })
        .where(eq(service.id, input.id))
        .returning();

      return result;
    }),

  deleteService: protectedProcedure
    .input(
      z.object({
        id: z.string(),
        organizationId: z.string(),
      }),
    )
    .mutation(async ({ input, ctx }) => {
      // Check if user has permission to delete services for this organization
      const isMember = await checkOrganizationMembership(
        ctx.userId,
        input.organizationId,
        "owner",
      );
      requireAuth(
        isMember,
        "You don't have permission to delete services for this organization",
      );

      return db.delete(service).where(eq(service.id, input.id));
    }),
};
