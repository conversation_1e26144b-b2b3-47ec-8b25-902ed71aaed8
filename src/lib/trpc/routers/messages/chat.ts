import { z } from "zod";
import { protectedProcedure } from "@/lib/trpc/trpc";
import {
  findOrCreateChat,
  getBidWithRelations,
  getJobWithRelations,
} from "@/lib/trpc/utils/entities";
import {
  checkJobOwnership,
  checkOrganizationMembership,
  checkPropertyOwnership,
  requireAuth,
} from "@/lib/trpc/utils/permissions";

export const chatRouter = {
  ensureChat: protectedProcedure
    .input(
      z
        .object({
          bidId: z.string().optional(),
          jobId: z.string().optional(),
        })
        .refine((data) => !!data.bidId || !!data.jobId, {
          message: "Either bidId or jobId must be provided",
        }),
    )
    .mutation(async ({ input, ctx }) => {
      if (input.bidId) {
        const bid = await getBidWithRelations(input.bidId);

        // Check permissions
        const isPropertyOwner = await checkJobOwnership(ctx.userId, bid.jobId);
        const isBidder = await checkOrganizationMembership(
          ctx.userId,
          bid.organizationId,
        );

        requireAuth(
          isPropertyOwner || isBidder,
          "You don't have permission to access this chat",
        );

        return findOrCreateChat({ bidId: input.bidId });
      }

      if (input.jobId) {
        const job = await getJobWithRelations(input.jobId);

        // Check permissions
        const isPropertyOwner = await checkPropertyOwnership(
          ctx.userId,
          job.propertyId,
        );
        const isProfessional = ctx.role === "contractor";

        requireAuth(
          isPropertyOwner || !!isProfessional,
          "You don't have permission to access this chat",
        );

        return findOrCreateChat({ jobId: input.jobId });
      }
    }),
};
