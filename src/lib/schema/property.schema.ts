import validator from "validator";
import { z } from "zod";

export const propertySchema = z.object({
  name: z.string().min(1, "Name is required"),
  imageUrl: z.string(),
  address: z.object({
    street: z.string().min(1, "Street is required"),
    city: z.string().min(1, "City is required"),
    state: z.string().min(1, "State is required"),
    zip: z
      .string()
      .min(1, "Zip is required")
      .refine((value) => validator.isPostalCode(value, "US"), {
        message: "Please enter a valid US ZIP code",
      }),
  }),
});

export type PropertyFormData = z.infer<typeof propertySchema>;
