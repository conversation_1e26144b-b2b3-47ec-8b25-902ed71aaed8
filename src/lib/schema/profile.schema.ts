import { z } from "zod";

export const profileFormSchema = z.object({
  name: z.string().min(2, { message: "Name must be at least 2 characters." }),
  settings: z.object({
    notifications: z.object({
      email: z.object({
        marketing: z.boolean().default(true),
        jobUpdates: z.boolean().default(true),
        messages: z.boolean().default(true),
        bids: z.boolean().default(true),
      }),
      push: z.object({
        enabled: z.boolean().default(false),
        jobUpdates: z.boolean().default(true),
        messages: z.boolean().default(true),
        bids: z.boolean().default(true),
      }),
    }),
    theme: z.enum(["light", "dark", "system"]).default("system"),
  }),
});

export type ProfileFormData = z.infer<typeof profileFormSchema>;
