import { z } from "zod";

export const bidSchema = z.object({
  name: z.string().min(1, "Bid name is required"),
  amount: z.number().min(1, "Bid amount is required"),
  description: z.string().min(10, "Please provide a detailed description"),
  estimatedDuration: z.number().min(1, "Estimated duration is required"),
});

export const acceptBidSchema = z.object({
  bidId: z.string().min(1, "Bid ID is required"),
  notes: z.string().optional(),
});

export const withdrawBidSchema = z.object({
  bidId: z.string().min(1, "Bid ID is required"),
  reason: z.string().optional(),
});

export type BidFormData = z.infer<typeof bidSchema>;
export type AcceptBidFormData = z.infer<typeof acceptBidSchema>;
export type WithdrawBidFormData = z.infer<typeof withdrawBidSchema>;
