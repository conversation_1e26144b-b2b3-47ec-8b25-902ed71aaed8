import { z } from "zod";

export const scheduleSchema = z.object({
  jobId: z.string(),
  proposedStartDate: z.date(),
  proposedEndDate: z.date(),
  notes: z.string().optional(),
});

export const scheduleFormSchema = z.object({
  startDate: z.date(),
  endDate: z.date(),
  notes: z.string().optional(),
});

export type ScheduleFormData = z.infer<typeof scheduleFormSchema>;
export type ScheduleProposalData = z.infer<typeof scheduleSchema>;
