import { PageLayout } from "@/components/page-layout";
import { PropertyForm } from "@/components/properties/property-form";
import { getQueryClient, trpc } from "@/components/trpc/server";

export default async function EditPropertyPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const queryClient = getQueryClient();
  const property = await queryClient.fetchQuery(
    trpc.properties.one.queryOptions({ id })
  );

  if (!property) {
    throw new Error("Property not found");
  }

  return (
    <PageLayout title="Edit Property">
      <div className="p-8">
        <PropertyForm initialData={property} />
      </div>
    </PageLayout>
  );
}
