import { Suspense } from "react";
import { PropertyGridSkeleton } from "@/components/loading-states";
import { PageLayout } from "@/components/page-layout";
import { NewProperty } from "@/components/properties/new-property";
import { PropertiesContent } from "@/components/properties/properties-content";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";

export default async function PropertiesPage() {
  const queryClient = getQueryClient();

  // Prefetch properties data on the server
  await queryClient.prefetchQuery(trpc.properties.list.queryOptions());

  return (
    <PageLayout title="Properties">
      <div className="flex h-full gap-4">
        <HydrateClient>
          <Suspense fallback={<PropertyGridSkeleton />}>
            <PropertiesContent />
          </Suspense>
        </HydrateClient>

        <div className="flex h-full items-center justify-center">
          <NewProperty />
        </div>
      </div>
    </PageLayout>
  );
}
