"use client";

import { useQuery } from "@tanstack/react-query";
import { notFound, useParams } from "next/navigation";
import { ContractorForm } from "@/components/contractor/contractor-form";
import { PageLayout } from "@/components/page-layout";
import { useTRPC } from "@/components/trpc/client";

export default function EditContractorPage() {
  const trpc = useTRPC();
  const { id } = useParams<{ id: string }>();
  const { data: organization, isLoading } = useQuery(
    trpc.contractor.getById.queryOptions({ id })
  );

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!organization || organization.id !== id) {
    return notFound();
  }

  return (
    <PageLayout title="Edit Contractor">
      <div className="p-8">
        <ContractorForm initialData={organization} />
      </div>
    </PageLayout>
  );
}
