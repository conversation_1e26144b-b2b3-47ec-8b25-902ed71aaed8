"use client";

import { useQuery } from "@tanstack/react-query";
import { notFound, useParams } from "next/navigation";
import { ContractorForm } from "@/components/contractor/contractor-form";
import { CrewSettings } from "@/components/contractor/crew-settings";
import { PageLayout } from "@/components/page-layout";
import { useTRPC } from "@/components/trpc/client";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";

export default function ContractorSettingsPage() {
  const trpc = useTRPC();
  const { id } = useParams<{ id: string }>();
  const { data: organization, isLoading } = useQuery(
    trpc.contractor.getById.queryOptions({ id }),
  );

  if (isLoading) {
    return <div>Loading...</div>;
  }

  if (!organization) {
    return notFound();
  }

  return (
    <PageLayout title="Contractor Settings">
      <div className="p-8">
        <Tabs defaultValue="general">
          <TabsList>
            <TabsTrigger value="general">General</TabsTrigger>
            <TabsTrigger value="crew">Crew</TabsTrigger>
          </TabsList>
          <TabsContent value="general" className="mt-4">
            <ContractorForm initialData={organization} />
          </TabsContent>
          <TabsContent value="crew" className="mt-4">
            <CrewSettings />
          </TabsContent>
        </Tabs>
      </div>
    </PageLayout>
  );
}
