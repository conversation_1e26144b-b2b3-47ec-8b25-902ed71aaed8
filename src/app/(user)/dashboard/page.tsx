import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { ContractorDashboard } from "@/components/contractor/contractor-dashboard";
import { StatsOverview } from "@/components/dashboard/stats-overview";
import { HomeownerDashboard } from "@/components/homeowner/homeowner-dashboard";
import { PageLayout } from "@/components/page-layout";
import { auth } from "@/lib/auth";

export default async function DashboardPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  if (!session?.user.onboardingComplete) {
    redirect("/onboarding");
  }

  // Determine if user is a homeowner or professional
  const isProfessional = session?.user?.role === "contractor";

  return (
    <PageLayout title={isProfessional ? "Contractor Dashboard" : "Dashboard"}>
      <div className="space-y-8">
        {/* Stats Overview with queries moved inside */}
        <StatsOverview isProfessional={isProfessional} />

        {/* Content based on user type */}
        {isProfessional ? <ContractorDashboard /> : <HomeownerDashboard />}
      </div>
    </PageLayout>
  );
}
