import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import { PageLayout } from "@/components/page-layout";
import { auth } from "@/lib/auth";
import { prefetchParallel, trpc } from "@/components/trpc/server";
import { PerformanceBoundary } from "@/components/performance/performance-provider";
import { LazyWrapper } from "@/lib/performance/react-optimizations";
import { DashboardSkeleton } from "@/components/performance/lazy-routes";

// Lazy load heavy dashboard components
import { createFeatureComponent } from "@/lib/performance";

const LazyContractorDashboard = createFeatureComponent(
  () => import("@/components/contractor/contractor-dashboard"),
  "ContractorDashboard",
  false
);

const LazyHomeownerDashboard = createFeatureComponent(
  () => import("@/components/homeowner/homeowner-dashboard"),
  "HomeownerDashboard",
  false
);

const LazyStatsOverview = createFeatureComponent(
  () => import("@/components/dashboard/stats-overview"),
  "StatsOverview",
  true // Preload this as it's critical
);

/**
 * Optimized Dashboard Page with Performance Enhancements
 * 
 * This page demonstrates:
 * - Server-side prefetching with parallel queries
 * - Lazy loading of heavy components
 * - Performance monitoring boundaries
 * - Smart component loading based on user role
 */
export default async function OptimizedDashboardPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  if (!session?.user.onboardingComplete) {
    redirect("/onboarding");
  }

  // Determine if user is a homeowner or professional
  const isProfessional = session?.user?.role === "contractor";
  const userId = session.user.id;

  // Smart prefetching based on user role
  if (isProfessional) {
    await prefetchParallel([
      trpc.jobs.listForContractor.queryOptions({ 
        contractorId: userId,
        limit: 10,
        includeCompleted: false 
      }),
      trpc.bids.listForContractor.queryOptions({ 
        contractorId: userId,
        limit: 5 
      }),
      trpc.contractor.getStats.queryOptions({ contractorId: userId }),
      trpc.messages.getUnreadCount.queryOptions({ userId }),
    ]);
  } else {
    await prefetchParallel([
      trpc.jobs.listForUser.queryOptions({ 
        limit: 10,
        includeCompleted: false 
      }),
      trpc.properties.list.queryOptions({ limit: 5 }),
      trpc.homeowner.getStats.queryOptions({ userId }),
      trpc.messages.getUnreadCount.queryOptions({ userId }),
    ]);
  }

  return (
    <PageLayout title={isProfessional ? "Contractor Dashboard" : "Dashboard"}>
      <PerformanceBoundary 
        componentName="DashboardPage" 
        threshold={200}
      >
        <div className="space-y-8">
          {/* Stats Overview - Critical component, preloaded */}
          <PerformanceBoundary 
            componentName="StatsOverview" 
            threshold={100}
          >
            <Suspense fallback={<StatsOverviewSkeleton />}>
              <LazyStatsOverview isProfessional={isProfessional} />
            </Suspense>
          </PerformanceBoundary>

          {/* Role-based dashboard content - Lazy loaded */}
          <PerformanceBoundary 
            componentName={isProfessional ? "ContractorDashboard" : "HomeownerDashboard"} 
            threshold={300}
          >
            {isProfessional ? (
              <LazyWrapper
                fallback={<ContractorDashboardSkeleton />}
                rootMargin="50px"
                className="min-h-[400px]"
              >
                <Suspense fallback={<ContractorDashboardSkeleton />}>
                  <LazyContractorDashboard />
                </Suspense>
              </LazyWrapper>
            ) : (
              <LazyWrapper
                fallback={<HomeownerDashboardSkeleton />}
                rootMargin="50px"
                className="min-h-[400px]"
              >
                <Suspense fallback={<HomeownerDashboardSkeleton />}>
                  <LazyHomeownerDashboard />
                </Suspense>
              </LazyWrapper>
            )}
          </PerformanceBoundary>
        </div>
      </PerformanceBoundary>
    </PageLayout>
  );
}

// ============================================================================
// LOADING SKELETONS
// ============================================================================

function StatsOverviewSkeleton() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
      {[...Array(4)].map((_, i) => (
        <div key={i} className="p-6 border rounded-lg space-y-2">
          <div className="h-4 bg-muted rounded animate-pulse" />
          <div className="h-8 bg-muted rounded animate-pulse" />
          <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
        </div>
      ))}
    </div>
  );
}

function ContractorDashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Active Jobs Section */}
      <div className="space-y-4">
        <div className="h-6 bg-muted rounded animate-pulse w-32" />
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="p-4 border rounded-lg space-y-3">
              <div className="h-5 bg-muted rounded animate-pulse" />
              <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
              <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
              <div className="flex justify-between">
                <div className="h-4 bg-muted rounded animate-pulse w-16" />
                <div className="h-4 bg-muted rounded animate-pulse w-20" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Recent Bids Section */}
      <div className="space-y-4">
        <div className="h-6 bg-muted rounded animate-pulse w-28" />
        <div className="space-y-2">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="p-3 border rounded space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
            </div>
          ))}
        </div>
      </div>
    </div>
  );
}

function HomeownerDashboardSkeleton() {
  return (
    <div className="space-y-6">
      {/* Active Projects Section */}
      <div className="space-y-4">
        <div className="h-6 bg-muted rounded animate-pulse w-36" />
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          {[...Array(2)].map((_, i) => (
            <div key={i} className="p-4 border rounded-lg space-y-3">
              <div className="h-5 bg-muted rounded animate-pulse" />
              <div className="h-4 bg-muted rounded animate-pulse w-3/4" />
              <div className="h-4 bg-muted rounded animate-pulse w-1/2" />
              <div className="flex justify-between">
                <div className="h-4 bg-muted rounded animate-pulse w-16" />
                <div className="h-4 bg-muted rounded animate-pulse w-20" />
              </div>
            </div>
          ))}
        </div>
      </div>

      {/* Properties Section */}
      <div className="space-y-4">
        <div className="h-6 bg-muted rounded animate-pulse w-24" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="p-3 border rounded space-y-2">
              <div className="h-4 bg-muted rounded animate-pulse" />
              <div className="h-3 bg-muted rounded animate-pulse w-2/3" />
            </div>
          ))}
        </div>
      </div>

      {/* Quick Actions */}
      <div className="space-y-4">
        <div className="h-6 bg-muted rounded animate-pulse w-32" />
        <div className="grid grid-cols-2 md:grid-cols-4 gap-3">
          {[...Array(4)].map((_, i) => (
            <div key={i} className="h-20 bg-muted rounded animate-pulse" />
          ))}
        </div>
      </div>
    </div>
  );
}

// ============================================================================
// METADATA AND EXPORTS
// ============================================================================

export const metadata = {
  title: "Dashboard - TradeCrews",
  description: "Your personalized dashboard for managing projects and connections",
};

// Export for potential dynamic imports
export { OptimizedDashboardPage };
