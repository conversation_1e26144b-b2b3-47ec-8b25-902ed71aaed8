"use client";

import {
  CalendarClockIcon,
  ClipboardListIcon,
  LayoutDashboardIcon,
  MapIcon,
} from "lucide-react";
import { AppSidebar } from "@/components/app-sidebar";
import { OrganizationProvider } from "@/components/contexts/organization-context";
import { PopupChat } from "@/components/ui/popup-chat";
import { SidebarInset, SidebarProvider } from "@/components/ui/sidebar";
import { useSession } from "@/lib/auth-client";

export default function UserLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <SidebarProvider>
      <OrganizationProvider>
        <UserLayoutContent>{children}</UserLayoutContent>
      </OrganizationProvider>
    </SidebarProvider>
  );
}

function UserLayoutContent({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  const { data: session } = useSession();

  const isHomeowner = session?.user.role === "homeowner";

  const menu = [
    {
      title: "Dashboard",
      href: "/dashboard",
      icon: LayoutDashboardIcon,
      visible: true,
    },
    {
      title: "Properties",
      href: "/properties",
      icon: MapIcon,
      visible: isHomeowner,
    },
    {
      title: "My Projects",
      href: "/projects",
      icon: ClipboardListIcon,
      visible: true,
    },
    {
      title: "Calendar",
      href: "/calendar",
      icon: CalendarClockIcon,
      visible: !isHomeowner,
    },
  ];

  return (
    <>
      <AppSidebar menu={menu} />
      <SidebarInset>
        {children}
        <PopupChat
          userName={session?.user?.name}
          userAvatar={session?.user?.image}
        />
      </SidebarInset>
    </>
  );
}
