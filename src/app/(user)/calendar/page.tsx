import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { Suspense } from "react";
import { CalendarContent } from "@/components/calendar/calendar-content";
import { PageLayout } from "@/components/page-layout";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { auth } from "@/lib/auth";

function CalendarSkeleton() {
  return (
    <div className="p-6">
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Schedule Calendar</CardTitle>
            </CardHeader>
            <CardContent>
              <Skeleton className="h-[400px] w-full" />
            </CardContent>
          </Card>
        </div>
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Jobs</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {[...Array(3)].map((_, i) => (
                  // biome-ignore lint/suspicious/noArrayIndexKey: Static skeleton items
                  <Skeleton key={i} className="h-20 w-full" />
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}

export default async function CalendarPage() {
  const session = await auth.api.getSession({ headers: await headers() });

  if (!session?.user) {
    redirect("/sign-in");
  }

  // Only contractors have calendars
  if (session.user.role !== "contractor") {
    redirect("/dashboard");
  }

  const queryClient = getQueryClient();

  // Get user's organization first
  const organization = await queryClient.fetchQuery(
    trpc.contractor.getForUser.queryOptions()
  );

  if (!organization) {
    return (
      <PageLayout title="Job Calendar">
        <div className="p-6">
          <Card>
            <CardHeader>
              <CardTitle>No Organization Found</CardTitle>
            </CardHeader>
            <CardContent>
              <p className="text-muted-foreground">
                You need to create an organization to view your calendar.
              </p>
            </CardContent>
          </Card>
        </div>
      </PageLayout>
    );
  }

  // Prefetch scheduled jobs data on the server
  await queryClient.prefetchQuery(
    trpc.jobs.listScheduledForOrganization.queryOptions({
      organizationId: organization.id,
    })
  );

  return (
    <PageLayout title="Job Calendar">
      <HydrateClient>
        <Suspense fallback={<CalendarSkeleton />}>
          <CalendarContent organizationId={organization.id} />
        </Suspense>
      </HydrateClient>
    </PageLayout>
  );
}
