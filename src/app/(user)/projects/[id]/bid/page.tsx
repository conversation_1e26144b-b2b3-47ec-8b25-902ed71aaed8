import { MapPinIcon } from "lucide-react";
import { headers } from "next/headers";
import { redirect } from "next/navigation";
import { BidForm } from "@/components/bid/bid-form";
import { PageLayout } from "@/components/page-layout";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { auth } from "@/lib/auth";

export default async function BidJobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const session = await auth.api.getSession({ headers: await headers() });
  const user = session?.user;

  // Redirect if not logged in
  if (!user) {
    redirect("/sign-in");
  }

  // Redirect if not a professional
  if (user.role !== "contractor") {
    redirect("/dashboard");
  }

  const queryClient = getQueryClient();
  const job = await queryClient.fetchQuery(
    trpc.jobs.getById.queryOptions({ id }),
  );

  // Redirect if job not found or not published
  if (!job || job.status !== "PUBLISHED") {
    redirect("/dashboard");
  }

  return (
    <PageLayout title={`Submit Bid: ${job.name}`}>
      <div className="p-8">
        <div className="mx-auto max-w-2xl">
          <Card>
            <CardHeader>
              <CardTitle>Submit Your Bid</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="mb-6">
                <h3 className="font-medium">Project Details</h3>
                <p className="text-muted-foreground">
                  Property: {job.property.name}
                </p>
                <p className="text-muted-foreground">Budget: ${job.budget}</p>
                {"distance" in job && (
                  <p className="mt-1 flex items-center text-muted-foreground">
                    <MapPinIcon className="mr-1 h-4 w-4" />
                    Distance: {Number(job.distance).toFixed(1)} miles away
                  </p>
                )}
              </div>
              <BidForm jobId={id} />
            </CardContent>
          </Card>
        </div>
      </div>
    </PageLayout>
  );
}
