import { JobForm } from "@/components/job/job-form";
import { PageLayout } from "@/components/page-layout";
import { caller } from "@/lib/trpc";

export default async function EditJobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const job = await caller.jobs.getById({ id });

  if (!job) {
    throw new Error("Job not found");
  }

  return (
    <PageLayout title="Edit Job">
      <div className="p-8">
        <JobForm initialData={job} propertyId={job.property.id} />
      </div>
    </PageLayout>
  );
}
