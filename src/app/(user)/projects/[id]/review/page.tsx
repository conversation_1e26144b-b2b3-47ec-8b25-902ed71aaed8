import { redirect } from "next/navigation";
import { ReviewForm } from "@/components/job/review-form";
import { PageLayout } from "@/components/page-layout";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useSession } from "@/lib/auth-client";

export default async function ReviewJobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { data: session } = useSession();
  const { id } = await params;

  if (!session || !session?.user) {
    redirect("/sign-in");
  }

  const queryClient = getQueryClient();
  const job = await queryClient.fetchQuery(
    trpc.jobs.getById.queryOptions({ id }),
  );

  if (!job || job.status !== "COMPLETED") {
    redirect(`/projects/${id}`);
  }

  // Determine if user is homeowner or contractor
  const isHomeowner = job.property.userId === session.user.id;

  // Find the contractor organization if there's an accepted bid
  const acceptedBid = job.bids.find((bid) => bid.status === "ACCEPTED");
  const contractorOrg = acceptedBid?.organization;

  // Check if user has already submitted a review
  const reviews = await queryClient.fetchQuery(
    trpc.reviews.listForJob.queryOptions({ jobId: id }),
  );

  const userReviewType = isHomeowner ? "CONTRACTOR_REVIEW" : "HOMEOWNER_REVIEW";
  const hasReviewed = reviews?.some(
    (review) =>
      review.reviewerId === session.user.id &&
      review.reviewType === userReviewType,
  );

  if (hasReviewed) {
    redirect(`/projects/${id}`);
  }

  let homeownerName = "Homeowner";

  if (!isHomeowner) {
    const homeowner = await queryClient.fetchQuery(
      trpc.users.getById.queryOptions({
        id: job.property.userId,
      }),
    );
    homeownerName = homeowner?.name as string;
  }

  return (
    <PageLayout title="Submit Review">
      <div className="p-8">
        <div className="mx-auto max-w-2xl">
          <Card className="mb-6">
            <CardHeader>
              <CardTitle>Project: {job.name}</CardTitle>
            </CardHeader>
            <CardContent>
              <p>Property: {job.property.name}</p>
              <p>
                Status: Completed on {job.completedAt?.toLocaleDateString()}
              </p>
            </CardContent>
          </Card>

          {isHomeowner && contractorOrg ? (
            <ReviewForm
              jobId={id}
              reviewType="CONTRACTOR_REVIEW"
              targetName={contractorOrg.name}
            />
          ) : (
            <ReviewForm
              jobId={id}
              reviewType="HOMEOWNER_REVIEW"
              targetName={homeownerName}
            />
          )}
        </div>
      </div>
    </PageLayout>
  );
}
