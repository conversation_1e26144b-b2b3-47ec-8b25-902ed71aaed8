import { Suspense } from "react";
import { AdminDashboardContent } from "@/components/admin/admin-dashboard-content";
import { Head<PERSON> } from "@/components/header";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";
import { Skeleton } from "@/components/ui/skeleton";

export default async function Dashboard() {
  const queryClient = getQueryClient();

  // Prefetch admin data on the server
  await Promise.all([
    queryClient.prefetchQuery(trpc.admin.getStats.queryOptions()),
    queryClient.prefetchQuery(trpc.jobs.list.queryOptions({ limit: 5 })),
  ]);

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <Header title="Admin Dashboard" />
      <HydrateClient>
        <Suspense fallback={<AdminDashboardSkeleton />}>
          <AdminDashboardContent />
        </Suspense>
      </HydrateClient>
    </div>
  );
}

function AdminDashboardSkeleton() {
  return (
    <div className="space-y-4">
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Skeleton className="h-32" />
        <Skeleton className="h-32" />
        <Skeleton className="h-32" />
        <Skeleton className="h-32" />
      </div>
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-7">
        <Skeleton className="h-80 md:col-span-4" />
        <Skeleton className="h-80 md:col-span-3" />
      </div>
    </div>
  );
}
