import { format } from "date-fns";
import { caller } from "@/lib/trpc";

export default async function JobPage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const job = await caller.jobs.getById({ id });

  return (
    <main className="p-8">
      <h1>
        <strong>Job:</strong> {job?.name}
      </h1>
      <h2>
        <strong>Property:</strong> {job?.property.name}
      </h2>
      <h2>
        <strong>Bids</strong>
      </h2>
      <ul>
        {job?.bids.map((bid, index) => (
          <li key={bid.id}>
            <h3>
              {index + 1}. {bid.name}
            </h3>
            <p>Created: {format(bid.createdAt, "PPP")}</p>
            <p>Updated: {format(bid.createdAt, "PPP")}</p>
          </li>
        ))}
      </ul>
      <h2>
        <strong>Tasks</strong>
      </h2>
      <ul>
        {job?.tasks.map((task, index) => (
          <li key={task.id}>
            <h3>
              {index + 1}. {task.name}
            </h3>
            <p>Created: {format(task.createdAt, "PPP")}</p>
            <p>Updated: {format(task.createdAt, "PPP")}</p>
          </li>
        ))}
      </ul>
    </main>
  );
}
