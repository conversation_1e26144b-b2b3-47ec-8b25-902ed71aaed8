import { Suspense } from "react";
import { AdminJobsContent } from "@/components/admin/admin-jobs-content";
import { Head<PERSON> } from "@/components/header";
import { getQueryClient, HydrateClient, trpc } from "@/components/trpc/server";
import { Skeleton } from "@/components/ui/skeleton";

export default async function JobsPage() {
  const queryClient = getQueryClient();

  // Prefetch jobs data on the server
  await queryClient.prefetchQuery(trpc.jobs.list.queryOptions());

  return (
    <div className="flex-1 space-y-4 p-8 pt-6">
      <Header title="Jobs Management" />
      <HydrateClient>
        <Suspense fallback={<AdminJobsSkeleton />}>
          <AdminJobsContent />
        </Suspense>
      </HydrateClient>
    </div>
  );
}

function AdminJobsSkeleton() {
  return (
    <div className="space-y-4">
      <Skeleton className="h-10 w-full" />
      <Skeleton className="h-64 w-full" />
    </div>
  );
}
