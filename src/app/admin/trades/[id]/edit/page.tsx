"use server";

import { TradeForm } from "@/components/trade/trade-form";
import { getQueryClient, trpc } from "@/components/trpc/server";

export default async function EditTradePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const queryClient = getQueryClient();
  const trade = await queryClient.fetchQuery(
    trpc.trades.one.queryOptions({ id })
  );

  return (
    <div className="p-8">
      <h1 className="mb-6 font-bold text-2xl">Edit Trade</h1>
      <TradeForm initialData={trade} />
    </div>
  );
}
