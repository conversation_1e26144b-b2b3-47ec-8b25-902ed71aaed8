import { Header } from "@/components/header";
import { TemplateForm } from "@/components/template/template-form";
import { getQueryClient, trpc } from "@/components/trpc/server";

export default async function EditTemplatePage({
  params,
}: {
  params: Promise<{ id: string }>;
}) {
  const { id } = await params;
  const queryClient = getQueryClient();
  const template = await queryClient.fetchQuery(
    trpc.templates.one.queryOptions({ id })
  );

  return (
    <>
      <Header title="Edit Template" />
      <div className="p-8">
        <h1 className="mb-6 font-bold text-2xl">Edit Project Template</h1>
        <TemplateForm initialData={template} />
      </div>
    </>
  );
}
