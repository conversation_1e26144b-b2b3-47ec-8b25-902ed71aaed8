import { PlusIcon } from "lucide-react";
import Link from "next/link";
import { <PERSON><PERSON> } from "@/components/header";
import { getQueryClient, trpc } from "@/components/trpc/server";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { formatCurrency } from "@/lib/utils";

export default async function TemplatesPage() {
  const queryClient = getQueryClient();
  const templates = await queryClient.fetchQuery(
    trpc.templates.list.queryOptions(),
  );

  return (
    <>
      <Header title="Project Templates" />
      <div className="p-8">
        <div className="mb-6 flex justify-between">
          <h1 className="font-bold text-2xl">Project Templates</h1>
          <Button asChild className="bg-orange-600 hover:bg-orange-700">
            <Link href="/admin/templates/new">
              <PlusIcon className="mr-2 h-4 w-4" />
              New Template
            </Link>
          </Button>
        </div>

        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Description</TableHead>
                <TableHead>Budget</TableHead>
                <TableHead>Duration</TableHead>
                <TableHead>Tasks</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {templates.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={6} className="h-24 text-center">
                    No templates found. Create your first template.
                  </TableCell>
                </TableRow>
              ) : (
                templates.map((template) => (
                  <TableRow key={template.id}>
                    <TableCell className="font-medium">
                      {template.name}
                    </TableCell>
                    <TableCell className="max-w-3xl truncate">
                      {template.description}
                    </TableCell>
                    <TableCell>{formatCurrency(template.budget)}</TableCell>
                    <TableCell>{template.estimatedDuration} days</TableCell>
                    <TableCell>{template.tasks.length}</TableCell>
                    <TableCell className="text-right">
                      <Link
                        href={`/admin/templates/${template.id}/edit`}
                        className="mr-4 text-blue-500 hover:text-blue-600"
                      >
                        Edit
                      </Link>
                      <Link
                        href={`/admin/templates/${template.id}/delete`}
                        className="text-red-500 hover:text-red-600"
                      >
                        Delete
                      </Link>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>
    </>
  );
}
