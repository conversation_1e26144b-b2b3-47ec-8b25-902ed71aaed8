"use client";

import Image from "next/image";
import Link from "next/link";
import logo from "@/assets/images/tc-logomark.webp";

export default function NotFound() {
  return (
    <main className="flex min-h-screen flex-col items-center justify-center bg-gray-100">
      <div className="flex flex-col items-center rounded-lg bg-white p-8 shadow-md">
        <Image src={logo} alt="TradeCrews Logo" width={64} className="mb-4" />
        <h1 className="mb-2 font-bold text-5xl text-gray-800">404</h1>
        <p className="mb-6 text-gray-600 text-lg">
          Oops! The page you're looking for doesn't exist.
        </p>
        <Link
          href="/"
          className="rounded bg-primary px-6 py-2 font-semibold text-white transition hover:bg-primary/90"
        >
          Go Home
        </Link>
      </div>
    </main>
  );
}
