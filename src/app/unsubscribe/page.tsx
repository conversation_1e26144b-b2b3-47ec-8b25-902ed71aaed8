"use client";

import { useMutation } from "@tanstack/react-query";
import { useSearchParams } from "next/navigation";
import { Suspense, useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";

export default function UnsubscribePage() {
  return (
    <Suspense>
      <Unsubscribe />
    </Suspense>
  );
}

function Unsubscribe() {
  const searchParams = useSearchParams();
  const userId = searchParams.get("userId");
  const email = searchParams.get("email");
  const [unsubscribed, setUnsubscribed] = useState(false);
  const trpc = useTRPC();

  const unsubscribe = useMutation(
    trpc.users.updateSettings.mutationOptions({
      onSuccess: () => {
        setUnsubscribed(true);
        toast.success("Successfully unsubscribed from emails");
      },
      onError: (error) => {
        toast.error(`Failed to unsubscribe: ${error.message}`);
      },
    }),
  );

  const handleUnsubscribe = () => {
    if (userId) {
      unsubscribe.mutate({
        notifications: {
          email: {
            marketing: false,
          },
        },
      });
    }
  };

  return (
    <div className="flex min-h-screen items-center justify-center bg-gray-50 p-4">
      <Card className="w-full max-w-md">
        <CardHeader>
          <CardTitle>Email Preferences</CardTitle>
          <CardDescription>
            Manage your email notification settings
          </CardDescription>
        </CardHeader>
        <CardContent>
          {unsubscribed ? (
            <div className="text-center">
              <h2 className="mb-2 font-semibold text-xl">
                Unsubscribed Successfully
              </h2>
              <p className="mb-4 text-gray-600">
                You have been unsubscribed from marketing emails. You may still
                receive important account-related notifications.
              </p>
            </div>
          ) : (
            <div className="text-center">
              <p className="mb-4 text-gray-600">
                {email ? (
                  <>
                    Are you sure you want to unsubscribe{" "}
                    <strong>{email}</strong> from marketing emails?
                  </>
                ) : (
                  "Are you sure you want to unsubscribe from marketing emails?"
                )}
              </p>
              <Button
                onClick={handleUnsubscribe}
                disabled={unsubscribe.isPending || !userId}
                className="w-full"
              >
                {unsubscribe.isPending ? "Processing..." : "Unsubscribe"}
              </Button>
              {!userId && (
                <p className="mt-4 text-red-500 text-sm">
                  Invalid unsubscribe link. Please contact support.
                </p>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
