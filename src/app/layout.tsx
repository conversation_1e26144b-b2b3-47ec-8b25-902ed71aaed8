import type { <PERSON>ada<PERSON> } from "next";
import { <PERSON><PERSON><PERSON> } from "next/font/google";
import { PostHogProvider } from "@/components/posthog-provider";
import { ServiceWorkerInit } from "@/components/service-worker-init";
import { ThemeProvider } from "@/components/theme-provider";
import { TRPCReactProvider } from "@/components/trpc/client";
import { Toaster } from "@/components/ui/sonner";

import "@/styles/globals.css";

export const metadata: Metadata = {
  title: "TradeCrews",
  description: "Connecting homeowners with the trades",
  appleWebApp: { title: "TradeCrews" },
  manifest: "/manifest.json",
};

const geist = Geist({
  subsets: ["latin"],
  variable: "--font-geist-sans",
});

export default function RootLayout({
  children,
}: Readonly<{ children: React.ReactNode }>) {
  return (
    <html
      lang="en"
      className={`${geist.variable} h-full`}
      suppressHydrationWarning
    >
      <head />
      <body className="h-full">
        <ThemeProvider
          attribute="class"
          defaultTheme="system"
          enableSystem
          disableTransitionOnChange
        >
          <PostHogProvider>
            <TRPCReactProvider>
              <ServiceWorkerInit />
              {children}
              <Toaster />
            </TRPCReactProvider>
          </PostHogProvider>
        </ThemeProvider>
      </body>
    </html>
  );
}
