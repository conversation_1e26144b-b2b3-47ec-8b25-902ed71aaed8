"use client";

import { ContractorOnboarding } from "@/components/contractor/contractor-onboarding";
import { HomeownerOnboarding } from "@/components/homeowner/homeowner-onboarding";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import { useSession } from "@/lib/auth-client";

export default function OnboardingPage() {
  const { data } = useSession();
  const user = data?.user;

  return (
    <div className="flex h-full items-center justify-center">
      <Card className="w-[56rem]">
        <CardHeader>
          <CardTitle>Welcome to TradeCrews.</CardTitle>
          <CardDescription>
            You're on your way to a better home.
          </CardDescription>
        </CardHeader>
        <CardContent>
          {user?.role === "homeowner" && <HomeownerOnboarding />}
          {user?.role === "contractor" && <ContractorOnboarding />}
        </CardContent>
      </Card>
    </div>
  );
}
