"use client";

import { useQuery } from "@tanstack/react-query";
import Link from "next/link";
import { formatShortDate } from "@/lib/utils";
import { useTRPC } from "../trpc/client";
import { buttonVariants } from "../ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../ui/table";

export function JobTable() {
  const trpc = useTRPC();
  const { data: jobs } = useQuery(trpc.jobs.listForUser.queryOptions());

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Starts At</TableHead>
          <TableHead>Deadline</TableHead>
          <TableHead>Budget</TableHead>
          <TableHead>Status</TableHead>

          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {jobs?.map((job) => (
          <TableRow key={job.id} className="border-b">
            <TableCell>{job.name}</TableCell>
            <TableCell>{formatShortDate(job.startsAt)}</TableCell>
            <TableCell>{formatShortDate(job.deadline)}</TableCell>
            <TableCell>${job.budget}</TableCell>
            <TableCell>{job.status}</TableCell>
            <TableCell className="text-right">
              <Link
                href={`/jobs/${job.id}/edit`}
                className="mr-4 text-blue-500 hover:text-blue-600"
              >
                Edit
              </Link>
              <Link
                href={`/jobs/${job.id}/delete`}
                className={buttonVariants({
                  variant: "destructive",
                })}
              >
                Delete
              </Link>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
