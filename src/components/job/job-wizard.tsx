"use client";

import { useQuery } from "@tanstack/react-query";
import { BriefcaseIcon, ZapIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useState } from "react";
import { JobForm } from "@/components/job/job-form";
import { QuickHireFlow } from "@/components/job/quick-hire-flow";
import { Button } from "@/components/ui/button";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { useTRPC } from "../trpc/client";

interface JobWizardProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  propertyId?: string;
}

type JobType = "STANDARD" | "QUICK_HIRE";
type WizardStep = "type" | "details" | "quickHire";

export function JobWizard({ open, onOpenChange, propertyId }: JobWizardProps) {
  const router = useRouter();
  const [jobType, setJobType] = useState<JobType | null>(null);
  const [step, setStep] = useState<WizardStep>("type");
  const [jobId, setJobId] = useState<string | null>(null);
  const [selectedTradeId, setSelectedTradeId] = useState<string | null>(null);

  const trpc = useTRPC();
  const { data: quickHireTrades } = useQuery(
    trpc.trades.listAvailableForQuickHire.queryOptions(),
  );

  const handleTypeSelect = (type: JobType) => {
    setJobType(type);
    if (type === "STANDARD") {
      setStep("details");
    } else {
      // For quick hire, we need to create a job first
      // This will be handled in the next step
      setStep("details");
    }
  };

  const handleJobCreated = (id: string, tradeId?: string) => {
    setJobId(id);
    if (tradeId) {
      setSelectedTradeId(tradeId);
    }

    if (jobType === "QUICK_HIRE") {
      setStep("quickHire");
    } else {
      // For standard jobs, we're done after creation
      onOpenChange(false);
      router.push(`/jobs/${id}`);
    }
  };

  const handleBack = () => {
    if (step === "details" || step === "quickHire") {
      setStep("type");
      setJobType(null);
    }
  };

  const handleClose = () => {
    setStep("type");
    setJobType(null);
    setJobId(null);
    setSelectedTradeId(null);
    onOpenChange(false);
  };

  useEffect(() => {
    const handleWizardBack = () => handleBack();
    const handleWizardCancel = () => handleClose();

    window.addEventListener("job-wizard-back", handleWizardBack);
    window.addEventListener("job-wizard-cancel", handleWizardCancel);

    return () => {
      window.removeEventListener("job-wizard-back", handleWizardBack);
      window.removeEventListener("job-wizard-cancel", handleWizardCancel);
    };
  });

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-5xl">
        <DialogHeader>
          <DialogTitle>
            {step === "type" && "New Project"}
            {step === "details" &&
              (jobType === "STANDARD"
                ? "Project Details"
                : "Quick Hire Details")}
            {step === "quickHire" && "Select Professional"}
          </DialogTitle>
          <DialogDescription>
            {step === "type" && "Choose the type of project you want to create"}
            {step === "details" && "Fill in the details for your project"}
            {step === "quickHire" &&
              "Select a professional for your quick hire job"}
          </DialogDescription>
        </DialogHeader>

        {step === "type" && (
          <div className="grid grid-cols-2 gap-4 py-4">
            <button
              type="button"
              className="cursor-pointer rounded-lg border p-6 text-center hover:border-tradecrews-orange-500 hover:bg-tradecrews-orange-50"
              onClick={() => handleTypeSelect("STANDARD")}
              onKeyDown={(e) =>
                e.key === "Enter" && handleTypeSelect("STANDARD")
              }
              tabIndex={0}
            >
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-tradecrews-orange-100">
                <BriefcaseIcon className="h-6 w-6 text-tradecrews-orange-600" />
              </div>
              <h3 className="mb-2 font-medium">Standard Project</h3>
              <p className="text-muted-foreground text-sm">
                Create a detailed project and collect bids from multiple
                professionals
              </p>
            </button>

            <button
              type="button"
              className={`cursor-pointer rounded-lg border p-6 text-center hover:border-tradecrews-orange-500 hover:bg-tradecrews-orange-50 ${
                !quickHireTrades?.length ? "cursor-not-allowed opacity-50" : ""
              }`}
              onClick={() =>
                quickHireTrades?.length ? handleTypeSelect("QUICK_HIRE") : null
              }
            >
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-blue-100">
                <ZapIcon className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="mb-2 font-medium">Quick Hire</h3>
              <p className="text-muted-foreground text-sm">
                Directly hire a professional for small or recurring jobs
              </p>
              {!quickHireTrades?.length && (
                <p className="mt-2 text-red-500 text-xs">
                  No professionals available for quick hire in your area
                </p>
              )}
            </button>
          </div>
        )}

        {step === "details" && (
          <div className="max-h-[60vh] overflow-y-auto py-4">
            <JobForm
              propertyId={propertyId}
              jobType={jobType || "STANDARD"}
              inWizard={true}
              onJobCreated={handleJobCreated}
            />
          </div>
        )}

        {step === "quickHire" && jobId && selectedTradeId && (
          <div className="max-h-[60vh] overflow-y-auto py-4">
            <QuickHireFlow
              jobId={jobId}
              tradeId={selectedTradeId}
              onComplete={handleClose}
            />
          </div>
        )}

        <DialogFooter>
          {step !== "type" && step !== "details" && (
            <Button variant="outline" onClick={handleBack}>
              Back
            </Button>
          )}
          {step !== "details" && (
            <Button variant="outline" onClick={handleClose}>
              Cancel
            </Button>
          )}
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
