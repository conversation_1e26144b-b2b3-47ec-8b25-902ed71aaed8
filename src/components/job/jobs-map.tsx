"use client";

import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "react-leaflet";
import "leaflet/dist/leaflet.css";
import { Icon } from "leaflet";
import defaultIconPng from "leaflet/dist/images/marker-icon.png";
import defaultShadowPng from "leaflet/dist/images/marker-shadow.png";
import type { Job, Organization } from "@/db/schema";

export interface JobsMapProps {
  jobs: Job[] | null | undefined;
  organization: Organization;
}

const defaultIcon = new Icon({
  //@ts-ignore
  iconUrl: defaultIconPng,
  iconSize: [25, 41],
  iconAnchor: [12, 41],
  popupAnchor: [1, -34],
  shadowSize: [41, 41],
  //@ts-ignore
  shadowUrl: defaultShadowPng,
});

export default function JobsMap({ jobs, organization }: JobsMapProps) {
  const orgLocation = organization.address?.location;

  return (
    <>
      <MapContainer
        center={[orgLocation?.x || 0, orgLocation?.y || 0]}
        zoom={10}
        className="h-[60vh]"
      >
        <TileLayer
          url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
          attribution={`&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors`}
        />
        <Marker
          position={[orgLocation?.x || 0, orgLocation?.y || 0]}
          icon={defaultIcon}
        >
          <Popup>
            <b>{organization?.name}</b>
            <br />
            Your location
          </Popup>
        </Marker>
        {jobs?.map((job) => {
          if (job.property?.address?.location) {
            const jobLocation = job.property.address.location;
            return (
              <Marker
                key={job.id}
                position={[jobLocation.x, jobLocation.y]}
                icon={defaultIcon}
              >
                <Popup>
                  <b>{job.name}</b>
                  <br />
                  {job.property.name}
                  <br />
                  {job.distance
                    ? `Distance: ${Number(job.distance).toFixed(1)} miles`
                    : ""}
                  <br />
                  Budget: ${job.budget}
                </Popup>
              </Marker>
            );
          }
          return null;
        })}
      </MapContainer>
    </>
  );
}
