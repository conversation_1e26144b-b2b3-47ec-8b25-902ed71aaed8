import { useMutation, useQuery } from "@tanstack/react-query";
import { Loader2, StarIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { Avatar, AvatarFallback, AvatarImage } from "../ui/avatar";
import { Badge } from "../ui/badge";
import { Button } from "../ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "../ui/card";

interface QuickHireFlowProps {
  jobId: string;
  tradeId: string;
  onComplete?: () => void;
}

export function QuickHireFlow({
  jobId,
  tradeId,
  onComplete,
}: QuickHireFlowProps) {
  const trpc = useTRPC();
  const router = useRouter();
  const [selectedOrgId, setSelectedOrgId] = useState<string | null>(null);
  const [selectedServiceId, setSelectedServiceId] = useState<string | null>(
    null,
  );

  const { data: professionals, isLoading } = useQuery(
    trpc.jobs.findProfessionalsForQuickHire.queryOptions({
      tradeId,
    }),
  );

  // Get services for selected organization
  const { data: services } = useQuery(
    trpc.contractor.getServices.queryOptions(
      { organizationId: selectedOrgId || "" },
      { enabled: !!selectedOrgId },
    ),
  );

  const directHire = useMutation(
    trpc.jobs.quickHire.mutationOptions({
      onSuccess: () => {
        toast.success("Professional hired successfully!");
        router.push(`/jobs/${jobId}`);
      },
      onError: (error) => {
        toast.error(`Error hiring professional: ${error.message}`);
      },
    }),
  );

  const handleHire = () => {
    if (!selectedOrgId || !selectedServiceId) return;

    const selectedService = services?.find(
      (service) => service.id === selectedServiceId,
    );
    if (!selectedService) return;

    directHire.mutate(
      {
        jobId,
        organizationId: selectedOrgId,
        amount: selectedService.price,
        estimatedDuration: selectedService.duration || 0,
      },
      {
        onSuccess: () => {
          toast.success("Professional hired successfully!");
          if (onComplete) {
            onComplete();
          } else {
            router.push(`/jobs/${jobId}`);
          }
        },
        onError: (error) => {
          toast.error(`Error hiring professional: ${error.message}`);
        },
      },
    );
  };

  if (isLoading) {
    return (
      <div className="flex justify-center p-8">
        <Loader2 className="h-8 w-8 animate-spin text-muted-foreground" />
      </div>
    );
  }

  if (!professionals) {
    return (
      <div className="py-8 text-center">
        <h2 className="mb-2 font-bold text-2xl">No Professionals Available</h2>
        <p className="mb-4 text-muted-foreground">
          There are no professionals in this trade who have opted in for quick
          hire jobs in your area.
        </p>
        <Button onClick={() => router.push(`/jobs/${jobId}`)} variant="outline">
          Back to Job
        </Button>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {!selectedOrgId ? (
        // First step: Choose a professional
        <>
          <div className="text-center">
            <h2 className="font-bold text-2xl">Choose a Professional</h2>
            <p className="text-muted-foreground">
              Select a professional to hire directly for your project
            </p>
          </div>

          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
            {professionals?.map((org) => (
              <Card
                key={org.id}
                className={`cursor-pointer transition-all ${
                  selectedOrgId === org.id
                    ? "border-orange-500 ring-2 ring-orange-500"
                    : ""
                }`}
                onClick={() => setSelectedOrgId(org.id)}
              >
                <CardHeader className="pb-2">
                  <div className="flex items-center gap-2">
                    <Avatar className="h-10 w-10">
                      <AvatarImage src={org.logoUrl || ""} alt={org.name} />
                      <AvatarFallback>
                        {org.name.substring(0, 2).toUpperCase()}
                      </AvatarFallback>
                    </Avatar>
                    <div>
                      <CardTitle className="text-lg">{org.name}</CardTitle>
                      <CardDescription>{org.trade?.name}</CardDescription>
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <p className="line-clamp-2 text-muted-foreground text-sm">
                    {org.description}
                  </p>
                  <div className="mt-2 flex items-center gap-1">
                    <StarIcon className="h-4 w-4 text-yellow-500" />
                    <span className="font-medium text-sm">4.8</span>
                    <span className="text-muted-foreground text-xs">
                      (24 reviews)
                    </span>
                  </div>
                </CardContent>
                <CardFooter>
                  <Badge variant="outline" className="w-full justify-center">
                    {org.bidsCount} completed jobs
                  </Badge>
                </CardFooter>
              </Card>
            ))}
          </div>

          <div className="flex justify-center">
            <Button
              onClick={() => {}}
              disabled={!selectedOrgId}
              className="bg-orange-600 hover:bg-orange-700"
            >
              Continue
            </Button>
          </div>
        </>
      ) : (
        // Second step: Choose a service
        <>
          <div className="text-center">
            <h2 className="font-bold text-2xl">Select a Service</h2>
            <p className="text-muted-foreground">
              Choose the service you'd like to book
            </p>
          </div>

          <div className="grid gap-4 md:grid-cols-2">
            {services?.map((service) => (
              <Card
                key={service.id}
                className={`cursor-pointer transition-all ${
                  selectedServiceId === service.id
                    ? "border-orange-500 ring-2 ring-orange-500"
                    : ""
                }`}
                onClick={() => setSelectedServiceId(service.id)}
              >
                <CardHeader>
                  <CardTitle>{service.name}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{service.description}</p>
                  <p className="mt-2 font-bold text-lg">${service.price}</p>
                  {service.duration && (
                    <p className="text-muted-foreground text-sm">
                      Estimated time: {service.duration} hours
                    </p>
                  )}
                </CardContent>
              </Card>
            ))}
          </div>

          <div className="flex justify-between">
            <Button variant="outline" onClick={() => setSelectedOrgId(null)}>
              Back
            </Button>
            <Button
              onClick={handleHire}
              disabled={!selectedServiceId || directHire.isPending}
              className="bg-orange-600 hover:bg-orange-700"
            >
              {directHire.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Hiring...
                </>
              ) : (
                "Hire Professional"
              )}
            </Button>
          </div>
        </>
      )}
    </div>
  );
}
