import { format } from "date-fns";
import Image from "next/image";
import Link from "next/link";
import type { Job } from "@/db/schema";
import { cn, getStatusVariant, JOB_STATUS_VARIANTS } from "@/lib/utils";
import { Badge } from "../ui/badge";
import { buttonVariants } from "../ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "../ui/card";

export function JobCard({ job }: { job: Job }) {
  return (
    <Card className="overflow-hidden">
      <CardHeader className="p-0">
        <div className="relative h-48 w-full overflow-hidden">
          <Image
            src={job.property?.imageUrl || ""}
            alt={job.property?.name || "Property"}
            width={400}
            height={300}
            className="h-full w-full object-cover transition-transform duration-500 group-hover:scale-105"
            priority={false}
          />
          <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
        </div>
      </CardHeader>
      <CardContent className="p-4">
        <div className="mb-2 flex items-center justify-between">
          <h3 className="font-semibold">Property: {job.property?.name}</h3>
          <Badge variant="outline">${job.budget}</Badge>
        </div>
        Bids: {job.bidsCount}
        <br />
        Status:{" "}
        <div className="mt-2 flex flex-wrap gap-2">
          <Badge variant={getStatusVariant(job.status, JOB_STATUS_VARIANTS)}>
            {job.status}
          </Badge>
          {job.jobType === "QUICK_HIRE" && (
            <Badge variant="secondary">Quick Hire</Badge>
          )}
          {job.isRecurring && (
            <Badge variant="outline">{job.recurringFrequency} Service</Badge>
          )}
        </div>
        <br />
        Created: {format(job.createdAt, "PPP")}
        {/* Rest of the card content */}
      </CardContent>
      <CardFooter className="border-t bg-muted/30 p-4">
        <div className="flex w-full gap-2">
          <Link
            className={cn(
              buttonVariants({ variant: "tc_blue", size: "sm" }),
              "flex-1",
            )}
            href={`/projects/${job.id}`}
          >
            View Details
          </Link>
          {!(job.status === "AWARDED" || job.status === "COMPLETED") && (
            <Link
              className={cn(
                buttonVariants({ variant: "tc_orange", size: "sm" }),
                "flex-1",
              )}
              href={`/projects/${job.id}/edit`}
            >
              Edit
            </Link>
          )}
        </div>
      </CardFooter>
    </Card>
  );
}
