"use client";

import { useQuery } from "@tanstack/react-query";
import { useState } from "react";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { Skeleton } from "@/components/ui/skeleton";
import type { JobTemplate } from "@/db/schema";
import { formatCurrency } from "@/lib/utils";

interface TemplateSelectorProps {
  onSelectTemplate: (template: JobTemplate) => void;
}

export function TemplateSelector({ onSelectTemplate }: TemplateSelectorProps) {
  const [open, setOpen] = useState(false);
  const trpc = useTRPC();
  const { data: templates, isLoading } = useQuery(
    trpc.templates.list.queryOptions(),
  );

  const handleSelectTemplate = (template: JobTemplate) => {
    onSelectTemplate(template);
    setOpen(false);
  };

  return (
    <Dialog open={open} onOpenChange={setOpen}>
      <DialogTrigger asChild>
        <Button variant="outline">Browse Templates</Button>
      </DialogTrigger>
      <DialogContent className="max-h-[80vh] overflow-y-auto sm:max-w-[800px]">
        <DialogHeader>
          <DialogTitle>Select a Project Template</DialogTitle>
          <DialogDescription>
            Choose a template to pre-fill your job details
          </DialogDescription>
        </DialogHeader>
        <div className="grid gap-4 py-4 md:grid-cols-2">
          {isLoading ? (
            Array.from({ length: 4 }).map((_, i) => (
              // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
              <Card key={i} className="overflow-hidden">
                <CardHeader className="p-4">
                  <Skeleton className="h-5 w-3/4" />
                  <Skeleton className="h-4 w-full" />
                </CardHeader>
                <CardContent className="p-4">
                  <Skeleton className="mb-2 h-4 w-full" />
                  <Skeleton className="h-4 w-2/3" />
                </CardContent>
                <CardFooter className="p-4">
                  <Skeleton className="h-9 w-full" />
                </CardFooter>
              </Card>
            ))
          ) : templates?.length === 0 ? (
            <div className="col-span-2 py-8 text-center">
              <p>No templates available</p>
            </div>
          ) : (
            templates?.map((template) => (
              <Card key={template.id}>
                <CardHeader>
                  <CardTitle>{template.name}</CardTitle>
                  <CardDescription>
                    {formatCurrency(template.budget)} •{" "}
                    {template.estimatedDuration} days
                  </CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="text-sm">{template.description}</p>
                  <div className="mt-2">
                    <p className="font-medium text-gray-500 text-xs">
                      {template.tasks.length} tasks included
                    </p>
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="default"
                    className="w-full"
                    onClick={() => handleSelectTemplate(template)}
                  >
                    Use This Template
                  </Button>
                </CardFooter>
              </Card>
            ))
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}
