import { Skeleton } from "@/components/ui/skeleton";

export function PropertyGridSkeleton({ count = 3 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
      {[...Array(count)].map((_, i) => (
        // biome-ignore lint/suspicious/noArrayIndexKey: No unique object
        <Skeleton key={i} className="h-[200px]" />
      ))}
    </div>
  );
}

export function DetailPageSkeleton() {
  return (
    <div className="container mx-auto p-6 md:p-8">
      <Skeleton className="mb-8 h-[400px] w-full" />
      <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
        <Skeleton className="h-[200px]" />
        <Skeleton className="h-[200px] md:col-span-2" />
      </div>
    </div>
  );
}
