import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import type { Organization } from "@/db/schema";

interface ContractorTableProps {
  organizations: Organization[];
}

export function ContractorTable({ organizations }: ContractorTableProps) {
  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Name</TableHead>
          <TableHead>Trade</TableHead>
          <TableHead>Quick Hire</TableHead>
          <TableHead className="text-right">Actions</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {organizations?.map((organization) => (
          <TableRow key={organization.id} className="border-b">
            <TableCell>{organization.name}</TableCell>
            <TableCell className="flex items-center gap-2">
              <div>{organization.trade?.name}</div>
            </TableCell>
            <TableCell>
              {organization.acceptsQuickHire ? (
                <Badge
                  variant="success"
                  className="bg-green-100 text-green-800"
                >
                  Available
                </Badge>
              ) : (
                <Badge variant="outline">Not Available</Badge>
              )}
            </TableCell>
            <TableCell className="text-right">
              <Link
                href={`/contractors/${organization.id}/edit`}
                className="mr-4 text-blue-500 hover:text-blue-600"
              >
                Edit
              </Link>
              <Link
                href={`/contractors/${organization.id}/settings`}
                className="mr-4 text-blue-500 hover:text-blue-600"
              >
                Settings
              </Link>
              <Link
                href={`/contractors/${organization.id}/delete`}
                className="text-red-500 hover:text-red-600"
              >
                Delete
              </Link>
            </TableCell>
          </TableRow>
        ))}
      </TableBody>
    </Table>
  );
}
