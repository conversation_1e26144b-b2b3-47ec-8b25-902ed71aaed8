"use client";

import { useMutation } from "@tanstack/react-query";
import { format } from "date-fns";
import { CalendarIcon, CheckIcon } from "lucide-react";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";

import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import {
  Card,
  CardContent,
  CardDescription,
  CardFooter,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import type { Schedule } from "@/db/schema";
import { formatShortDate } from "@/lib/utils";

interface ScheduleConfirmationProps {
  schedule: Schedule;
  userRole: string;
}

export function ScheduleConfirmation({
  schedule,
  userRole,
}: ScheduleConfirmationProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const [isOpen, setIsOpen] = useState(false);
  const [startDate, setStartDate] = useState<Date | undefined>(
    schedule.proposedStartDate
      ? new Date(schedule.proposedStartDate)
      : undefined,
  );
  const [endDate, setEndDate] = useState<Date | undefined>(
    schedule.proposedEndDate ? new Date(schedule.proposedEndDate) : undefined,
  );

  const confirmSchedule = useMutation(
    trpc.schedules.confirmSchedule.mutationOptions({
      onSuccess: () => {
        toast.success("Schedule confirmed successfully");
        setIsOpen(false);
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error confirming schedule: ${error.message}`);
      },
    }),
  );

  // Check if the current user is the proposer
  const isProposer = schedule.proposedByRole === userRole;

  function handleConfirm() {
    if (!startDate || !endDate) {
      toast.error("Please select both start and end dates");
      return;
    }

    confirmSchedule.mutate({
      scheduleId: schedule.id,
      confirmedStartDate: startDate,
      confirmedEndDate: endDate,
    });
  }

  if (schedule.status === "CONFIRMED") {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <CheckIcon className="h-5 w-5 text-green-500" />
            Schedule Confirmed
          </CardTitle>
          <CardDescription>
            Both parties have agreed to the following schedule
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Start Date:</span>
              <span>
                {formatShortDate(schedule.confirmedStartDate as Date)}
              </span>
            </div>
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">End Date:</span>
              <span>{formatShortDate(schedule.confirmedEndDate as Date)}</span>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (isProposer) {
    return (
      <Card>
        <CardHeader>
          <CardTitle>Schedule Proposed</CardTitle>
          <CardDescription>
            Waiting for the other party to confirm
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-2">
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Proposed Start:</span>
              <span>{format(new Date(schedule.proposedStartDate), "PPP")}</span>
            </div>
            <div className="flex items-center gap-2">
              <CalendarIcon className="h-4 w-4 text-muted-foreground" />
              <span className="font-medium">Proposed End:</span>
              <span>{format(new Date(schedule.proposedEndDate), "PPP")}</span>
            </div>
            {schedule.notes && (
              <div className="mt-4 rounded-md bg-muted p-3 text-sm">
                <p className="font-medium">Notes:</p>
                <p>{schedule.notes}</p>
              </div>
            )}
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle>Schedule Confirmation Needed</CardTitle>
        <CardDescription>
          The other party has proposed a schedule for this job
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-2">
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Proposed Start:</span>
            <span>{format(new Date(schedule.proposedStartDate), "PPP")}</span>
          </div>
          <div className="flex items-center gap-2">
            <CalendarIcon className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">Proposed End:</span>
            <span>{format(new Date(schedule.proposedEndDate), "PPP")}</span>
          </div>
          {schedule.notes && (
            <div className="mt-4 rounded-md bg-muted p-3 text-sm">
              <p className="font-medium">Notes:</p>
              <p>{schedule.notes}</p>
            </div>
          )}
        </div>
      </CardContent>
      <CardFooter>
        <Dialog open={isOpen} onOpenChange={setIsOpen}>
          <DialogTrigger asChild>
            <Button>Confirm Schedule</Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Confirm Job Schedule</DialogTitle>
              <DialogDescription>
                You can accept the proposed dates or adjust them before
                confirming.
              </DialogDescription>
            </DialogHeader>

            <div className="grid gap-4 py-4">
              <div className="space-y-2">
                <h3 className="font-medium text-sm">Start Date</h3>
                <Calendar
                  mode="single"
                  selected={startDate}
                  onSelect={setStartDate}
                  initialFocus
                />
              </div>

              <div className="space-y-2">
                <h3 className="font-medium text-sm">End Date</h3>
                <Calendar
                  mode="single"
                  selected={endDate}
                  onSelect={setEndDate}
                  initialFocus
                />
              </div>
            </div>

            <DialogFooter>
              <Button variant="outline" onClick={() => setIsOpen(false)}>
                Cancel
              </Button>
              <Button
                onClick={handleConfirm}
                disabled={confirmSchedule.isPending}
              >
                {confirmSchedule.isPending
                  ? "Confirming..."
                  : "Confirm Schedule"}
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </CardFooter>
    </Card>
  );
}
