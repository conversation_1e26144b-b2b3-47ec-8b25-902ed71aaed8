"use client";

import { driver } from "driver.js";
import { useEffect } from "react";
import "driver.js/dist/driver.css";
import { useLocalStorage } from "@/hooks/use-local-storage";

export function BidCreationTour() {
  const [hasSeenTour, setHasSeenTour] = useLocalStorage("seen-bid-tour", false);

  useEffect(() => {
    if (hasSeenTour) return;

    const driverInstance = driver({
      showProgress: true,
      steps: [
        {
          element: "[name='name']",
          popover: {
            title: "Bid Name",
            description: "Give your bid a descriptive name.",
            side: "bottom",
          },
        },
        {
          element: "[name='amount']",
          popover: {
            title: "Bid Amount",
            description: "Enter the amount you're bidding for this project.",
            side: "top",
          },
        },
        {
          element: "[name='description']",
          popover: {
            title: "Bid Description",
            description: "Provide details about your approach to the project.",
            side: "top",
          },
        },
        {
          element: "[name='estimatedDuration']",
          popover: {
            title: "Estimated Duration",
            description:
              "Specify how many days you expect the project to take.",
            side: "right",
          },
        },
        {
          element: "[type='submit']",
          popover: {
            title: "Submit Your Bid",
            description: "When you're ready, click here to submit your bid.",
            side: "top",
          },
        },
      ],
      onDestroyStarted: () => {
        setHasSeenTour(true);
      },
      onDestroyed: () => {
        setHasSeenTour(true);
      },
    });

    // Start the tour after a short delay to ensure all elements are loaded
    const timer = setTimeout(() => {
      driverInstance.drive();
    }, 1000);

    return () => {
      clearTimeout(timer);
      driverInstance.destroy();
    };
  }, [hasSeenTour, setHasSeenTour]);

  return (
    <div className="hidden">
      {/* This component doesn't render anything visible */}
    </div>
  );
}
