"use client";

import { useMutation } from "@tanstack/react-query";
import { useRouter } from "next/navigation";
import { useState } from "react";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";

interface AcceptBidFormProps {
  bidId: string;
}

export function AcceptBidForm({ bidId }: AcceptBidFormProps) {
  const router = useRouter();
  const trpc = useTRPC();
  const [confirmed, setConfirmed] = useState(false);

  const acceptBid = useMutation(
    trpc.bids.accept.mutationOptions({
      onSuccess: () => {
        toast.success("Bid accepted successfully");
        router.push(`/bids/${bidId}`);
        router.refresh();
      },
      onError: (error) => {
        toast.error(`Error accepting bid: ${error.message}`);
      },
    })
  );

  const handleAccept = () => {
    if (!confirmed) {
      toast.error("Please confirm that you want to accept this bid");
      return;
    }

    acceptBid.mutate({ id: bidId });
  };

  return (
    <div className="space-y-6">
      <div className="flex items-start space-x-2">
        <Checkbox
          id="confirm"
          checked={confirmed}
          onCheckedChange={(checked) => setConfirmed(checked as boolean)}
        />
        <div className="grid gap-1.5 leading-none">
          <Label
            htmlFor="confirm"
            className="font-medium text-sm leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
          >
            I understand that accepting this bid will:
          </Label>
          <ul className="text-muted-foreground text-sm">
            <li>• Award the project to this contractor</li>
            <li>• Reject all other bids automatically</li>
            <li>• Change the project status to "Awarded"</li>
          </ul>
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button
          type="button"
          variant="outline"
          onClick={() => router.push(`/bids/${bidId}`)}
        >
          Cancel
        </Button>
        <Button
          type="button"
          className="bg-green-600 hover:bg-green-700"
          onClick={handleAccept}
          disabled={acceptBid.isPending}
        >
          {acceptBid.isPending ? "Processing..." : "Accept Bid"}
        </Button>
      </div>
    </div>
  );
}
