import { type ReactNode } from "react";
import Image from "next/image";
import Link from "next/link";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>er, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Title } from "@/components/ui/card";
import { cn } from "@/lib/utils";

export interface EntityCardAction {
  label: string;
  href?: string;
  onClick?: () => void;
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "tc_blue" | "tc_orange";
  size?: "default" | "sm" | "lg" | "icon";
  disabled?: boolean;
  icon?: ReactNode;
}

export interface EntityCardBadge {
  label: string;
  variant?: "default" | "secondary" | "destructive" | "outline";
  className?: string;
}

export interface EntityCardMetaItem {
  icon: ReactNode;
  label: string;
  value: string | ReactNode;
  className?: string;
}

export interface EntityCardProps {
  // Core content
  title: string;
  subtitle?: string;
  description?: string;
  
  // Visual elements
  imageUrl?: string;
  imageAlt?: string;
  badges?: EntityCardBadge[];
  
  // Metadata
  metadata?: EntityCardMetaItem[];
  
  // Actions
  actions?: EntityCardAction[];
  primaryAction?: EntityCardAction;
  
  // Layout options
  variant?: "default" | "compact" | "detailed";
  orientation?: "vertical" | "horizontal";
  
  // Styling
  className?: string;
  hoverable?: boolean;
  
  // Custom content
  children?: ReactNode;
  headerExtra?: ReactNode;
  footerExtra?: ReactNode;
}

export function EntityCard({
  title,
  subtitle,
  description,
  imageUrl,
  imageAlt,
  badges = [],
  metadata = [],
  actions = [],
  primaryAction,
  variant = "default",
  orientation = "vertical",
  className,
  hoverable = true,
  children,
  headerExtra,
  footerExtra,
}: EntityCardProps) {
  const isHorizontal = orientation === "horizontal";
  const isCompact = variant === "compact";
  const isDetailed = variant === "detailed";

  const cardContent = (
    <Card 
      className={cn(
        "group relative flex h-full flex-col overflow-hidden transition-all duration-300",
        hoverable && "hover:shadow-md",
        isHorizontal && "flex-row",
        className
      )}
    >
      {/* Image Section */}
      {imageUrl && (
        <div className={cn(
          "relative overflow-hidden",
          isHorizontal ? "w-48 flex-shrink-0" : "h-48 w-full",
          isCompact && (isHorizontal ? "w-32" : "h-32"),
          !isCompact && !isHorizontal && "-mt-6"
        )}>
          <Image
            src={imageUrl}
            alt={imageAlt || title}
            fill
            className="object-cover transition-transform duration-500 group-hover:scale-105"
            priority={false}
          />
          {hoverable && (
            <div className="absolute inset-0 bg-gradient-to-t from-black/60 to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100" />
          )}
        </div>
      )}

      {/* Content Section */}
      <div className={cn(
        "flex flex-1 flex-col",
        isHorizontal && "p-4",
        !isHorizontal && imageUrl && "p-4"
      )}>
        {/* Header */}
        <CardHeader className={cn(
          "flex-shrink-0",
          isCompact ? "pb-2" : "pb-3",
          (isHorizontal || !imageUrl) && "pt-0"
        )}>
          <div className="flex items-start justify-between gap-2">
            <div className="flex-1 min-w-0">
              <CardTitle className={cn(
                "line-clamp-2",
                isCompact ? "text-base" : "text-lg"
              )}>
                {title}
              </CardTitle>
              {subtitle && (
                <p className="mt-1 text-muted-foreground text-sm line-clamp-1">
                  {subtitle}
                </p>
              )}
            </div>
            
            {/* Badges */}
            {badges.length > 0 && (
              <div className="flex flex-wrap gap-1">
                {badges.map((badge, index) => (
                  <Badge
                    key={index}
                    variant={badge.variant}
                    className={badge.className}
                  >
                    {badge.label}
                  </Badge>
                ))}
              </div>
            )}
            
            {headerExtra}
          </div>
        </CardHeader>

        {/* Content */}
        <CardContent className={cn(
          "flex-1",
          isCompact ? "py-2" : "py-3"
        )}>
          {description && (
            <p className={cn(
              "text-muted-foreground text-sm mb-3",
              isCompact ? "line-clamp-2" : "line-clamp-3"
            )}>
              {description}
            </p>
          )}

          {/* Metadata */}
          {metadata.length > 0 && (
            <div className={cn(
              "space-y-2",
              isDetailed && "grid grid-cols-2 gap-2 space-y-0"
            )}>
              {metadata.map((item, index) => (
                <div key={index} className={cn(
                  "flex items-center gap-2 text-sm",
                  item.className
                )}>
                  <span className="text-tradecrews-orange flex-shrink-0">
                    {item.icon}
                  </span>
                  <span className="text-muted-foreground flex-shrink-0">
                    {item.label}:
                  </span>
                  <span className="font-medium truncate">
                    {item.value}
                  </span>
                </div>
              ))}
            </div>
          )}

          {children}
        </CardContent>

        {/* Footer */}
        {(actions.length > 0 || primaryAction || footerExtra) && (
          <CardFooter className={cn(
            "flex-shrink-0 gap-2",
            isCompact ? "pt-2" : "pt-3",
            actions.length > 2 ? "flex-col space-y-2" : "flex-row justify-between"
          )}>
            <div className="flex gap-2 flex-wrap">
              {primaryAction && (
                <ActionButton action={primaryAction} isPrimary />
              )}
              {actions.map((action, index) => (
                <ActionButton key={index} action={action} />
              ))}
            </div>
            {footerExtra}
          </CardFooter>
        )}
      </div>
    </Card>
  );

  // Wrap in link if primary action has href
  if (primaryAction?.href && !primaryAction.onClick) {
    return (
      <Link href={primaryAction.href} className="block h-full">
        {cardContent}
      </Link>
    );
  }

  return cardContent;
}

function ActionButton({ 
  action, 
  isPrimary = false 
}: { 
  action: EntityCardAction; 
  isPrimary?: boolean;
}) {
  const buttonProps = {
    variant: action.variant || (isPrimary ? "tc_blue" : "outline"),
    size: action.size || "sm",
    disabled: action.disabled,
    onClick: action.onClick,
    className: cn(
      isPrimary && "flex-1",
      action.disabled && "opacity-50 cursor-not-allowed"
    ),
  };

  const content = (
    <>
      {action.icon && <span className="mr-2">{action.icon}</span>}
      {action.label}
    </>
  );

  if (action.href && !action.onClick) {
    return (
      <Button asChild {...buttonProps}>
        <Link href={action.href}>
          {content}
        </Link>
      </Button>
    );
  }

  return (
    <Button {...buttonProps}>
      {content}
    </Button>
  );
}
