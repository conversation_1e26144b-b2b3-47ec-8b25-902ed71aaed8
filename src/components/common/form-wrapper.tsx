"use client";

import { type ReactNode } from "react";
import { type FieldValues, type UseFormReturn } from "react-hook-form";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, <PERSON><PERSON><PERSON>er, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Form } from "@/components/ui/form";
import { Separator } from "@/components/ui/separator";
import { cn } from "@/lib/utils";

export interface FormAction {
  label: string;
  type?: "submit" | "button" | "reset";
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link" | "tc_blue" | "tc_orange";
  size?: "default" | "sm" | "lg";
  onClick?: () => void;
  disabled?: boolean;
  loading?: boolean;
  icon?: ReactNode;
}

export interface FormSection {
  title?: string;
  description?: string;
  children: ReactNode;
  className?: string;
}

export interface FormWrapperProps<TFieldValues extends FieldValues> {
  // Form configuration
  form: UseFormReturn<TFieldValues>;
  onSubmit: (data: TFieldValues) => void | Promise<void>;
  
  // Content
  title?: string;
  description?: string;
  sections?: FormSection[];
  children?: ReactNode;
  
  // Actions
  submitLabel?: string;
  submitIcon?: ReactNode;
  submitVariant?: FormAction["variant"];
  cancelLabel?: string;
  cancelHref?: string;
  onCancel?: () => void;
  actions?: FormAction[];
  
  // Layout
  variant?: "default" | "card" | "inline";
  layout?: "vertical" | "horizontal";
  maxWidth?: "sm" | "md" | "lg" | "xl" | "full";
  
  // State
  isLoading?: boolean;
  disabled?: boolean;
  
  // Styling
  className?: string;
  contentClassName?: string;
  
  // Behavior
  resetOnSubmit?: boolean;
  showRequiredIndicator?: boolean;
}

export function FormWrapper<TFieldValues extends FieldValues>({
  form,
  onSubmit,
  title,
  description,
  sections = [],
  children,
  submitLabel = "Save",
  submitIcon,
  submitVariant = "tc_blue",
  cancelLabel,
  cancelHref,
  onCancel,
  actions = [],
  variant = "default",
  layout = "vertical",
  maxWidth = "md",
  isLoading = false,
  disabled = false,
  className,
  contentClassName,
  resetOnSubmit = false,
}: FormWrapperProps<TFieldValues>) {
  const handleSubmit = async (data: TFieldValues) => {
    try {
      await onSubmit(data);
      if (resetOnSubmit) {
        form.reset();
      }
    } catch (error) {
      // Error handling is done in the onSubmit function
      console.error("Form submission error:", error);
    }
  };

  const maxWidthClasses = {
    sm: "max-w-sm",
    md: "max-w-md",
    lg: "max-w-lg",
    xl: "max-w-xl",
    full: "max-w-full",
  };

  const formContent = (
    <Form {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className={cn(
          "space-y-6",
          layout === "horizontal" && "space-y-4",
          className
        )}
      >
        {/* Header */}
        {(title || description) && variant !== "card" && (
          <div className="space-y-2">
            {title && (
              <h2 className="font-semibold text-2xl tracking-tight">
                {title}
              </h2>
            )}
            {description && (
              <p className="text-muted-foreground">
                {description}
              </p>
            )}
          </div>
        )}

        {/* Content */}
        <div className={cn(
          "space-y-6",
          layout === "horizontal" && "space-y-4",
          contentClassName
        )}>
          {/* Sections */}
          {sections.map((section, index) => (
            <FormSection
              key={index}
              title={section.title}
              description={section.description}
              className={section.className}
            >
              {section.children}
            </FormSection>
          ))}

          {/* Direct children */}
          {children}
        </div>

        {/* Actions */}
        <FormActions
          submitLabel={submitLabel}
          submitIcon={submitIcon}
          submitVariant={submitVariant}
          cancelLabel={cancelLabel}
          cancelHref={cancelHref}
          onCancel={onCancel}
          actions={actions}
          isLoading={isLoading}
          disabled={disabled}
          isValid={form.formState.isValid}
          isDirty={form.formState.isDirty}
        />
      </form>
    </Form>
  );

  // Card variant
  if (variant === "card") {
    return (
      <div className={cn("mx-auto", maxWidthClasses[maxWidth])}>
        <Card>
          {(title || description) && (
            <CardHeader>
              {title && <CardTitle>{title}</CardTitle>}
              {description && <CardDescription>{description}</CardDescription>}
            </CardHeader>
          )}
          <CardContent>
            {formContent}
          </CardContent>
        </Card>
      </div>
    );
  }

  // Default variant
  return (
    <div className={cn("mx-auto", maxWidthClasses[maxWidth])}>
      {formContent}
    </div>
  );
}

function FormSection({
  title,
  description,
  children,
  className,
}: {
  title?: string;
  description?: string;
  children: ReactNode;
  className?: string;
}) {
  if (!title && !description) {
    return <div className={className}>{children}</div>;
  }

  return (
    <div className={cn("space-y-4", className)}>
      {(title || description) && (
        <div className="space-y-1">
          {title && (
            <h3 className="font-medium text-lg">{title}</h3>
          )}
          {description && (
            <p className="text-muted-foreground text-sm">{description}</p>
          )}
          <Separator />
        </div>
      )}
      <div className="space-y-4">
        {children}
      </div>
    </div>
  );
}

function FormActions({
  submitLabel,
  submitIcon,
  submitVariant,
  cancelLabel,
  cancelHref,
  onCancel,
  actions,
  isLoading,
  disabled,
  isValid,
  isDirty,
}: {
  submitLabel: string;
  submitIcon?: ReactNode;
  submitVariant: FormAction["variant"];
  cancelLabel?: string;
  cancelHref?: string;
  onCancel?: () => void;
  actions: FormAction[];
  isLoading: boolean;
  disabled: boolean;
  isValid: boolean;
  isDirty: boolean;
}) {
  const hasCancel = cancelLabel || onCancel || cancelHref;
  const hasActions = actions.length > 0;

  if (!hasCancel && !hasActions) {
    return (
      <Button
        type="submit"
        variant={submitVariant}
        disabled={disabled || isLoading || !isValid}
        className="w-full"
      >
        {isLoading && (
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        )}
        {submitIcon && !isLoading && <span className="mr-2">{submitIcon}</span>}
        {isLoading ? "Saving..." : submitLabel}
      </Button>
    );
  }

  return (
    <div className="flex flex-col-reverse gap-2 sm:flex-row sm:justify-end">
      {/* Cancel button */}
      {hasCancel && (
        <Button
          type="button"
          variant="outline"
          onClick={onCancel}
          disabled={isLoading}
          asChild={!!cancelHref}
        >
          {cancelHref ? (
            <a href={cancelHref}>{cancelLabel || "Cancel"}</a>
          ) : (
            cancelLabel || "Cancel"
          )}
        </Button>
      )}

      {/* Custom actions */}
      {actions.map((action, index) => (
        <Button
          key={index}
          type={action.type || "button"}
          variant={action.variant}
          size={action.size}
          onClick={action.onClick}
          disabled={action.disabled || isLoading}
        >
          {action.loading && (
            <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
          )}
          {action.icon && !action.loading && (
            <span className="mr-2">{action.icon}</span>
          )}
          {action.label}
        </Button>
      ))}

      {/* Submit button */}
      <Button
        type="submit"
        variant={submitVariant}
        disabled={disabled || isLoading || !isValid}
      >
        {isLoading && (
          <div className="mr-2 h-4 w-4 animate-spin rounded-full border-2 border-current border-t-transparent" />
        )}
        {submitIcon && !isLoading && <span className="mr-2">{submitIcon}</span>}
        {isLoading ? "Saving..." : submitLabel}
      </Button>
    </div>
  );
}
