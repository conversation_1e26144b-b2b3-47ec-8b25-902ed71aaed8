"use client";

/**
 * Enhanced Link component with smart prefetching strategies
 * 
 * This component extends Next.js Link with advanced prefetching capabilities:
 * - Viewport-based prefetching
 * - Hover-based prefetching
 * - User behavior analysis
 * - Performance monitoring
 */

import Link from "next/link";
import { useRouter } from "next/navigation";
import { 
  forwardRef, 
  useCallback, 
  useEffect, 
  useRef, 
  useState,
  type ComponentProps,
  type ForwardedRef
} from "react";
import { useIntersectionObserver, useThrottle } from "@/lib/performance/react-optimizations";

// ============================================================================
// TYPES
// ============================================================================

interface PrefetchLinkProps extends Omit<ComponentProps<typeof Link>, 'prefetch'> {
  prefetchStrategy?: 'hover' | 'viewport' | 'immediate' | 'none';
  hoverDelay?: number;
  viewportMargin?: string;
  priority?: 'high' | 'medium' | 'low';
  analytics?: boolean;
  children: React.ReactNode;
}

// ============================================================================
// PREFETCH STRATEGIES
// ============================================================================

/**
 * Prefetch manager to avoid duplicate prefetches
 */
class PrefetchManager {
  private prefetchedRoutes = new Set<string>();
  private prefetchPromises = new Map<string, Promise<void>>();
  private maxPrefetches = 20;

  async prefetch(href: string, router: ReturnType<typeof useRouter>, priority: 'high' | 'medium' | 'low' = 'medium') {
    if (this.prefetchedRoutes.has(href)) {
      return;
    }

    if (this.prefetchedRoutes.size >= this.maxPrefetches) {
      // Remove oldest prefetch if we're at the limit
      const oldestRoute = Array.from(this.prefetchedRoutes)[0];
      this.prefetchedRoutes.delete(oldestRoute);
      this.prefetchPromises.delete(oldestRoute);
    }

    if (!this.prefetchPromises.has(href)) {
      const prefetchPromise = this.performPrefetch(href, router, priority);
      this.prefetchPromises.set(href, prefetchPromise);
    }

    return this.prefetchPromises.get(href);
  }

  private async performPrefetch(href: string, router: ReturnType<typeof useRouter>, priority: 'high' | 'medium' | 'low') {
    try {
      // Use Next.js router prefetch
      router.prefetch(href);
      this.prefetchedRoutes.add(href);
      
      console.log(`🚀 Prefetched route: ${href} (priority: ${priority})`);
    } catch (error) {
      console.warn(`Failed to prefetch route: ${href}`, error);
    }
  }

  isPrefetched(href: string): boolean {
    return this.prefetchedRoutes.has(href);
  }

  clear() {
    this.prefetchedRoutes.clear();
    this.prefetchPromises.clear();
  }
}

// Global prefetch manager instance
const prefetchManager = new PrefetchManager();

// ============================================================================
// ANALYTICS
// ============================================================================

interface LinkAnalytics {
  href: string;
  hoverCount: number;
  clickCount: number;
  prefetchTime?: number;
  clickTime?: number;
}

class LinkAnalyticsManager {
  private analytics = new Map<string, LinkAnalytics>();

  trackHover(href: string) {
    const existing = this.analytics.get(href) || {
      href,
      hoverCount: 0,
      clickCount: 0,
    };
    
    existing.hoverCount += 1;
    this.analytics.set(href, existing);
  }

  trackPrefetch(href: string) {
    const existing = this.analytics.get(href) || {
      href,
      hoverCount: 0,
      clickCount: 0,
    };
    
    existing.prefetchTime = Date.now();
    this.analytics.set(href, existing);
  }

  trackClick(href: string) {
    const existing = this.analytics.get(href) || {
      href,
      hoverCount: 0,
      clickCount: 0,
    };
    
    existing.clickCount += 1;
    existing.clickTime = Date.now();
    this.analytics.set(href, existing);

    // Log analytics for development
    if (process.env.NODE_ENV === 'development') {
      const prefetchBenefit = existing.prefetchTime && existing.clickTime 
        ? existing.clickTime - existing.prefetchTime 
        : null;
      
      console.log(`📊 Link Analytics for ${href}:`, {
        hovers: existing.hoverCount,
        clicks: existing.clickCount,
        prefetchBenefit: prefetchBenefit ? `${prefetchBenefit}ms ago` : 'not prefetched',
      });
    }
  }

  getAnalytics() {
    return Array.from(this.analytics.values());
  }
}

const analyticsManager = new LinkAnalyticsManager();

// ============================================================================
// PREFETCH LINK COMPONENT
// ============================================================================

export const PrefetchLink = forwardRef<HTMLAnchorElement, PrefetchLinkProps>(
  ({
    href,
    prefetchStrategy = 'hover',
    hoverDelay = 300,
    viewportMargin = '50px',
    priority = 'medium',
    analytics = false,
    children,
    onMouseEnter,
    onMouseLeave,
    onClick,
    ...props
  }, ref: ForwardedRef<HTMLAnchorElement>) => {
    const router = useRouter();
    const [isHovering, setIsHovering] = useState(false);
    const hoverTimeoutRef = useRef<NodeJS.Timeout>();
    const linkRef = useRef<HTMLAnchorElement>(null);

    // Viewport-based prefetching
    const { ref: intersectionRef, isVisible } = useIntersectionObserver({
      rootMargin: viewportMargin,
      threshold: 0.1,
      triggerOnce: true,
    });

    // Combine refs
    const combinedRef = useCallback((element: HTMLAnchorElement | null) => {
      linkRef.current = element;
      intersectionRef(element);
      if (typeof ref === 'function') {
        ref(element);
      } else if (ref) {
        ref.current = element;
      }
    }, [intersectionRef, ref]);

    // Throttled prefetch function
    const throttledPrefetch = useThrottle(
      useCallback(() => {
        prefetchManager.prefetch(href.toString(), router, priority);
        if (analytics) {
          analyticsManager.trackPrefetch(href.toString());
        }
      }, [href, router, priority, analytics]),
      100
    );

    // Handle viewport prefetching
    useEffect(() => {
      if (prefetchStrategy === 'viewport' && isVisible) {
        throttledPrefetch();
      }
    }, [prefetchStrategy, isVisible, throttledPrefetch]);

    // Handle immediate prefetching
    useEffect(() => {
      if (prefetchStrategy === 'immediate') {
        throttledPrefetch();
      }
    }, [prefetchStrategy, throttledPrefetch]);

    // Handle hover prefetching
    const handleMouseEnter = useCallback((e: React.MouseEvent<HTMLAnchorElement>) => {
      setIsHovering(true);
      
      if (analytics) {
        analyticsManager.trackHover(href.toString());
      }

      if (prefetchStrategy === 'hover') {
        hoverTimeoutRef.current = setTimeout(() => {
          throttledPrefetch();
        }, hoverDelay);
      }

      onMouseEnter?.(e);
    }, [prefetchStrategy, hoverDelay, throttledPrefetch, analytics, href, onMouseEnter]);

    const handleMouseLeave = useCallback((e: React.MouseEvent<HTMLAnchorElement>) => {
      setIsHovering(false);
      
      if (hoverTimeoutRef.current) {
        clearTimeout(hoverTimeoutRef.current);
      }

      onMouseLeave?.(e);
    }, [onMouseLeave]);

    const handleClick = useCallback((e: React.MouseEvent<HTMLAnchorElement>) => {
      if (analytics) {
        analyticsManager.trackClick(href.toString());
      }

      onClick?.(e);
    }, [analytics, href, onClick]);

    // Cleanup timeout on unmount
    useEffect(() => {
      return () => {
        if (hoverTimeoutRef.current) {
          clearTimeout(hoverTimeoutRef.current);
        }
      };
    }, []);

    return (
      <Link
        ref={combinedRef}
        href={href}
        prefetch={false} // We handle prefetching manually
        onMouseEnter={handleMouseEnter}
        onMouseLeave={handleMouseLeave}
        onClick={handleClick}
        {...props}
      >
        {children}
      </Link>
    );
  }
);

PrefetchLink.displayName = "PrefetchLink";

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Hook to get link analytics
 */
export function useLinkAnalytics() {
  const [analytics, setAnalytics] = useState<LinkAnalytics[]>([]);

  useEffect(() => {
    const interval = setInterval(() => {
      setAnalytics(analyticsManager.getAnalytics());
    }, 5000);

    return () => clearInterval(interval);
  }, []);

  return analytics;
}

/**
 * Hook to prefetch routes programmatically
 */
export function usePrefetch() {
  const router = useRouter();

  const prefetch = useCallback((href: string, priority: 'high' | 'medium' | 'low' = 'medium') => {
    return prefetchManager.prefetch(href, router, priority);
  }, [router]);

  const isPrefetched = useCallback((href: string) => {
    return prefetchManager.isPrefetched(href);
  }, []);

  return { prefetch, isPrefetched };
}

// ============================================================================
// EXPORTS
// ============================================================================

export { prefetchManager, analyticsManager };
export type { PrefetchLinkProps, LinkAnalytics };
