"use client";

/**
 * Performance Provider
 * 
 * This provider initializes and manages all performance optimizations including:
 * - Global performance monitoring
 * - Prefetch management
 * - Resource preloading
 * - Performance dashboard (dev only)
 */

import { createContext, useContext, useEffect, useRef, type ReactNode } from "react";
import { PerformanceDashboard } from "./performance-dashboard";
import { 
  globalPerformanceMonitor, 
  preloadCriticalResources,
  ViewportPrefetcher,
  BehaviorPrefetcher 
} from "@/lib/performance";

// ============================================================================
// CONTEXT
// ============================================================================

interface PerformanceContextValue {
  viewportPrefetcher: ViewportPrefetcher | null;
  behaviorPrefetcher: BehaviorPrefetcher | null;
  performanceMonitor: typeof globalPerformanceMonitor;
}

const PerformanceContext = createContext<PerformanceContextValue>({
  viewportPrefetcher: null,
  behaviorPrefetcher: null,
  performanceMonitor: globalPerformanceMonitor,
});

// ============================================================================
// PERFORMANCE PROVIDER
// ============================================================================

interface PerformanceProviderProps {
  children: ReactNode;
  enableDashboard?: boolean;
  criticalResources?: string[];
  prefetchConfig?: {
    viewport?: {
      rootMargin?: string;
      threshold?: number;
      maxPrefetches?: number;
    };
    behavior?: {
      hoverDelay?: number;
      maxPrefetches?: number;
    };
  };
}

export function PerformanceProvider({
  children,
  enableDashboard = process.env.NODE_ENV === 'development',
  criticalResources = [],
  prefetchConfig = {},
}: PerformanceProviderProps) {
  const viewportPrefetcherRef = useRef<ViewportPrefetcher | null>(null);
  const behaviorPrefetcherRef = useRef<BehaviorPrefetcher | null>(null);
  const initializedRef = useRef(false);

  // Initialize performance utilities
  useEffect(() => {
    if (initializedRef.current) return;
    initializedRef.current = true;

    // Initialize prefetchers
    viewportPrefetcherRef.current = new ViewportPrefetcher(prefetchConfig.viewport);
    behaviorPrefetcherRef.current = new BehaviorPrefetcher(prefetchConfig.behavior);

    // Preload critical resources
    if (criticalResources.length > 0) {
      preloadCriticalResources(criticalResources);
    }

    // Initialize Web Vitals tracking
    initializeWebVitals();

    // Cleanup on unmount
    return () => {
      viewportPrefetcherRef.current?.disconnect();
      globalPerformanceMonitor.disconnect();
    };
  }, [criticalResources, prefetchConfig]);

  // Initialize Web Vitals tracking
  const initializeWebVitals = () => {
    // Track Core Web Vitals
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      // Largest Contentful Paint (LCP)
      const lcpObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          globalPerformanceMonitor.recordMetric('webVitals.LCP', entry.startTime);
        }
      });
      lcpObserver.observe({ entryTypes: ['largest-contentful-paint'] });

      // First Input Delay (FID)
      const fidObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          const fidValue = (entry as any).processingStart - entry.startTime;
          globalPerformanceMonitor.recordMetric('webVitals.FID', fidValue);
        }
      });
      fidObserver.observe({ entryTypes: ['first-input'] });

      // Cumulative Layout Shift (CLS)
      let clsValue = 0;
      const clsObserver = new PerformanceObserver((list) => {
        for (const entry of list.getEntries()) {
          if (!(entry as any).hadRecentInput) {
            clsValue += (entry as any).value;
            globalPerformanceMonitor.recordMetric('webVitals.CLS', clsValue);
          }
        }
      });
      clsObserver.observe({ entryTypes: ['layout-shift'] });

      // Time to First Byte (TTFB)
      const navigationEntries = performance.getEntriesByType('navigation');
      if (navigationEntries.length > 0) {
        const navEntry = navigationEntries[0] as PerformanceNavigationTiming;
        const ttfb = navEntry.responseStart - navEntry.requestStart;
        globalPerformanceMonitor.recordMetric('webVitals.TTFB', ttfb);
      }
    }
  };

  const contextValue: PerformanceContextValue = {
    viewportPrefetcher: viewportPrefetcherRef.current,
    behaviorPrefetcher: behaviorPrefetcherRef.current,
    performanceMonitor: globalPerformanceMonitor,
  };

  return (
    <PerformanceContext.Provider value={contextValue}>
      {children}
      {enableDashboard && <PerformanceDashboard />}
    </PerformanceContext.Provider>
  );
}

// ============================================================================
// HOOKS
// ============================================================================

/**
 * Hook to access performance utilities
 */
export function usePerformance() {
  const context = useContext(PerformanceContext);
  if (!context) {
    throw new Error('usePerformance must be used within a PerformanceProvider');
  }
  return context;
}

/**
 * Hook to track component performance
 */
export function useComponentPerformance(componentName: string) {
  const { performanceMonitor } = usePerformance();
  const renderStartTime = useRef<number>();

  useEffect(() => {
    renderStartTime.current = performance.now();
    
    return () => {
      if (renderStartTime.current) {
        const renderTime = performance.now() - renderStartTime.current;
        performanceMonitor.recordMetric(`component.${componentName}.renderTime`, renderTime);
      }
    };
  });

  const trackCustomMetric = (metricName: string, value: number) => {
    performanceMonitor.recordMetric(`component.${componentName}.${metricName}`, value);
  };

  return { trackCustomMetric };
}

/**
 * Hook for viewport-based prefetching
 */
export function useViewportPrefetch() {
  const { viewportPrefetcher } = usePerformance();

  const observeElement = (element: HTMLElement, href: string) => {
    if (viewportPrefetcher && element) {
      viewportPrefetcher.observe(element, href);
    }
  };

  return { observeElement };
}

/**
 * Hook for behavior-based prefetching
 */
export function useBehaviorPrefetch() {
  const { behaviorPrefetcher } = usePerformance();

  const onLinkHover = (href: string, callback?: () => void) => {
    if (behaviorPrefetcher) {
      behaviorPrefetcher.onLinkHover(href, callback);
    }
  };

  const onLinkLeave = () => {
    if (behaviorPrefetcher) {
      behaviorPrefetcher.onLinkLeave();
    }
  };

  return { onLinkHover, onLinkLeave };
}

// ============================================================================
// PERFORMANCE BOUNDARY COMPONENT
// ============================================================================

/**
 * Performance boundary that catches and reports performance issues
 */
export function PerformanceBoundary({ 
  children, 
  componentName,
  threshold = 100 
}: { 
  children: ReactNode;
  componentName: string;
  threshold?: number;
}) {
  const { performanceMonitor } = usePerformance();
  const renderStartTime = useRef<number>();

  useEffect(() => {
    renderStartTime.current = performance.now();
    
    return () => {
      if (renderStartTime.current) {
        const renderTime = performance.now() - renderStartTime.current;
        
        if (renderTime > threshold) {
          console.warn(`🐌 Slow render detected in ${componentName}: ${renderTime.toFixed(2)}ms`);
        }
        
        performanceMonitor.recordMetric(`boundary.${componentName}.renderTime`, renderTime);
      }
    };
  });

  return <>{children}</>;
}

// ============================================================================
// EXPORTS
// ============================================================================

export type { PerformanceContextValue, PerformanceProviderProps };
