"use client";

/**
 * Performance Monitoring Dashboard
 * 
 * A development-only component that displays real-time performance metrics
 * including bundle sizes, render times, prefetch analytics, and more.
 */

import { useEffect, useState } from "react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Tabs, TabsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { useLinkAnalytics } from "./prefetch-link";
import { globalPerformanceMonitor } from "@/lib/performance";

// ============================================================================
// TYPES
// ============================================================================

interface PerformanceMetrics {
  navigation: {
    domContentLoaded: number;
    load: number;
    firstContentfulPaint?: number;
    largestContentfulPaint?: number;
  };
  resources: {
    javascript: number[];
    stylesheet: number[];
    image: number[];
    api: number[];
    other: number[];
  };
  memory?: {
    usedJSHeapSize: number;
    totalJSHeapSize: number;
    jsHeapSizeLimit: number;
  };
}

// ============================================================================
// PERFORMANCE DASHBOARD COMPONENT
// ============================================================================

export function PerformanceDashboard() {
  const [isVisible, setIsVisible] = useState(false);
  const [metrics, setMetrics] = useState<PerformanceMetrics | null>(null);
  const [webVitals, setWebVitals] = useState<Record<string, number>>({});
  const linkAnalytics = useLinkAnalytics();

  // Only show in development
  if (process.env.NODE_ENV !== 'development') {
    return null;
  }

  // Collect performance metrics
  useEffect(() => {
    const collectMetrics = () => {
      const performanceMetrics = globalPerformanceMonitor.getMetrics();
      
      // Get navigation timing
      const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming;
      
      // Get memory info if available
      const memory = (performance as any).memory;

      setMetrics({
        navigation: {
          domContentLoaded: navigation?.domContentLoadedEventEnd - navigation?.domContentLoadedEventStart || 0,
          load: navigation?.loadEventEnd - navigation?.loadEventStart || 0,
        },
        resources: {
          javascript: performanceMetrics['resource.javascript'] ? [performanceMetrics['resource.javascript'].avg] : [],
          stylesheet: performanceMetrics['resource.stylesheet'] ? [performanceMetrics['resource.stylesheet'].avg] : [],
          image: performanceMetrics['resource.image'] ? [performanceMetrics['resource.image'].avg] : [],
          api: performanceMetrics['resource.api'] ? [performanceMetrics['resource.api'].avg] : [],
          other: performanceMetrics['resource.other'] ? [performanceMetrics['resource.other'].avg] : [],
        },
        memory: memory ? {
          usedJSHeapSize: memory.usedJSHeapSize,
          totalJSHeapSize: memory.totalJSHeapSize,
          jsHeapSizeLimit: memory.jsHeapSizeLimit,
        } : undefined,
      });
    };

    collectMetrics();
    const interval = setInterval(collectMetrics, 5000);
    return () => clearInterval(interval);
  }, []);

  // Collect Web Vitals
  useEffect(() => {
    const observer = new PerformanceObserver((list) => {
      for (const entry of list.getEntries()) {
        if (entry.entryType === 'largest-contentful-paint') {
          setWebVitals(prev => ({ ...prev, LCP: entry.startTime }));
        }
        if (entry.entryType === 'first-input') {
          setWebVitals(prev => ({ ...prev, FID: (entry as any).processingStart - entry.startTime }));
        }
        if (entry.entryType === 'layout-shift' && !(entry as any).hadRecentInput) {
          setWebVitals(prev => ({ ...prev, CLS: (prev.CLS || 0) + (entry as any).value }));
        }
      }
    });

    observer.observe({ entryTypes: ['largest-contentful-paint', 'first-input', 'layout-shift'] });
    return () => observer.disconnect();
  }, []);

  if (!isVisible) {
    return (
      <div className="fixed bottom-4 right-4 z-50">
        <Button
          onClick={() => setIsVisible(true)}
          variant="outline"
          size="sm"
          className="bg-background/80 backdrop-blur-sm"
        >
          📊 Performance
        </Button>
      </div>
    );
  }

  return (
    <div className="fixed bottom-4 right-4 z-50 w-96 max-h-[80vh] overflow-auto">
      <Card className="bg-background/95 backdrop-blur-sm border-2">
        <CardHeader className="pb-2">
          <div className="flex items-center justify-between">
            <CardTitle className="text-sm">Performance Monitor</CardTitle>
            <Button
              onClick={() => setIsVisible(false)}
              variant="ghost"
              size="sm"
              className="h-6 w-6 p-0"
            >
              ✕
            </Button>
          </div>
        </CardHeader>
        <CardContent className="pt-0">
          <Tabs defaultValue="vitals" className="w-full">
            <TabsList className="grid w-full grid-cols-4 text-xs">
              <TabsTrigger value="vitals">Vitals</TabsTrigger>
              <TabsTrigger value="resources">Resources</TabsTrigger>
              <TabsTrigger value="prefetch">Prefetch</TabsTrigger>
              <TabsTrigger value="memory">Memory</TabsTrigger>
            </TabsList>

            <TabsContent value="vitals" className="space-y-2">
              <div className="text-xs font-medium">Web Vitals</div>
              <div className="space-y-1">
                {Object.entries(webVitals).map(([key, value]) => (
                  <div key={key} className="flex justify-between text-xs">
                    <span>{key}:</span>
                    <Badge variant={getVitalStatus(key, value)} className="text-xs">
                      {formatMetric(key, value)}
                    </Badge>
                  </div>
                ))}
              </div>
              
              {metrics && (
                <>
                  <div className="text-xs font-medium mt-3">Navigation</div>
                  <div className="space-y-1">
                    <div className="flex justify-between text-xs">
                      <span>DOM Ready:</span>
                      <span>{metrics.navigation.domContentLoaded.toFixed(0)}ms</span>
                    </div>
                    <div className="flex justify-between text-xs">
                      <span>Load Complete:</span>
                      <span>{metrics.navigation.load.toFixed(0)}ms</span>
                    </div>
                  </div>
                </>
              )}
            </TabsContent>

            <TabsContent value="resources" className="space-y-2">
              <div className="text-xs font-medium">Resource Loading</div>
              {metrics && (
                <div className="space-y-1">
                  {Object.entries(metrics.resources).map(([type, times]) => (
                    <div key={type} className="flex justify-between text-xs">
                      <span className="capitalize">{type}:</span>
                      <span>
                        {times.length > 0 ? `${times[0].toFixed(0)}ms` : 'N/A'}
                      </span>
                    </div>
                  ))}
                </div>
              )}
            </TabsContent>

            <TabsContent value="prefetch" className="space-y-2">
              <div className="text-xs font-medium">Prefetch Analytics</div>
              <div className="max-h-32 overflow-y-auto space-y-1">
                {linkAnalytics.length === 0 ? (
                  <div className="text-xs text-muted-foreground">No data yet</div>
                ) : (
                  linkAnalytics.slice(0, 5).map((link, index) => (
                    <div key={index} className="text-xs">
                      <div className="font-mono truncate">{link.href}</div>
                      <div className="flex justify-between text-muted-foreground">
                        <span>Hovers: {link.hoverCount}</span>
                        <span>Clicks: {link.clickCount}</span>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </TabsContent>

            <TabsContent value="memory" className="space-y-2">
              <div className="text-xs font-medium">Memory Usage</div>
              {metrics?.memory ? (
                <div className="space-y-1">
                  <div className="flex justify-between text-xs">
                    <span>Used:</span>
                    <span>{formatBytes(metrics.memory.usedJSHeapSize)}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Total:</span>
                    <span>{formatBytes(metrics.memory.totalJSHeapSize)}</span>
                  </div>
                  <div className="flex justify-between text-xs">
                    <span>Limit:</span>
                    <span>{formatBytes(metrics.memory.jsHeapSizeLimit)}</span>
                  </div>
                  <div className="w-full bg-muted rounded-full h-2 mt-2">
                    <div
                      className="bg-primary h-2 rounded-full"
                      style={{
                        width: `${(metrics.memory.usedJSHeapSize / metrics.memory.totalJSHeapSize) * 100}%`
                      }}
                    />
                  </div>
                </div>
              ) : (
                <div className="text-xs text-muted-foreground">
                  Memory info not available
                </div>
              )}
            </TabsContent>
          </Tabs>
        </CardContent>
      </Card>
    </div>
  );
}

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

function getVitalStatus(metric: string, value: number): "default" | "secondary" | "destructive" {
  switch (metric) {
    case 'LCP':
      return value <= 2500 ? 'default' : value <= 4000 ? 'secondary' : 'destructive';
    case 'FID':
      return value <= 100 ? 'default' : value <= 300 ? 'secondary' : 'destructive';
    case 'CLS':
      return value <= 0.1 ? 'default' : value <= 0.25 ? 'secondary' : 'destructive';
    default:
      return 'default';
  }
}

function formatMetric(metric: string, value: number): string {
  switch (metric) {
    case 'LCP':
    case 'FID':
      return `${value.toFixed(0)}ms`;
    case 'CLS':
      return value.toFixed(3);
    default:
      return value.toString();
  }
}

function formatBytes(bytes: number): string {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return `${parseFloat((bytes / Math.pow(k, i)).toFixed(1))} ${sizes[i]}`;
}
