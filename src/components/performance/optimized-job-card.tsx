"use client";

/**
 * Optimized Job Card Component
 * 
 * This component demonstrates performance optimizations including:
 * - React.memo with custom comparison
 * - Lazy loading of images
 * - Intersection observer for viewport-based loading
 * - Optimized prefetching
 * - Performance monitoring
 */

import { memo, useMemo, useState } from "react";
import Image from "next/image";
import { format } from "date-fns";
import { CalendarIcon, DollarSignIcon, MapPinIcon, ClockIcon } from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { PrefetchLink } from "./prefetch-link";
import { LazyWrapper, useRenderPerformance, useStableMemo } from "@/lib/performance/react-optimizations";

// ============================================================================
// TYPES
// ============================================================================

interface JobCardProps {
  job: {
    id: string;
    title: string;
    description: string;
    budget: number;
    location: string;
    createdAt: Date;
    status: 'open' | 'in_progress' | 'completed' | 'cancelled';
    trade: string;
    images?: string[];
    urgency: 'low' | 'medium' | 'high';
    bidCount?: number;
  };
  priority?: 'high' | 'medium' | 'low';
  showImages?: boolean;
  compact?: boolean;
  onCardClick?: (jobId: string) => void;
  className?: string;
}

// ============================================================================
// OPTIMIZED JOB CARD COMPONENT
// ============================================================================

const OptimizedJobCardComponent = ({
  job,
  priority = 'medium',
  showImages = true,
  compact = false,
  onCardClick,
  className,
}: JobCardProps) => {
  // Performance monitoring
  useRenderPerformance(`JobCard-${job.id}`);

  // Memoized calculations
  const formattedDate = useStableMemo(
    () => format(job.createdAt, 'MMM d, yyyy'),
    [job.createdAt],
    'formattedDate'
  );

  const statusColor = useStableMemo(() => {
    switch (job.status) {
      case 'open':
        return 'bg-green-100 text-green-800';
      case 'in_progress':
        return 'bg-blue-100 text-blue-800';
      case 'completed':
        return 'bg-gray-100 text-gray-800';
      case 'cancelled':
        return 'bg-red-100 text-red-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }, [job.status], 'statusColor');

  const urgencyColor = useStableMemo(() => {
    switch (job.urgency) {
      case 'high':
        return 'bg-red-100 text-red-800';
      case 'medium':
        return 'bg-yellow-100 text-yellow-800';
      case 'low':
        return 'bg-green-100 text-green-800';
      default:
        return 'bg-gray-100 text-gray-800';
    }
  }, [job.urgency], 'urgencyColor');

  const truncatedDescription = useStableMemo(
    () => job.description.length > 150 
      ? `${job.description.substring(0, 150)}...` 
      : job.description,
    [job.description],
    'truncatedDescription'
  );

  const jobUrl = `/projects/${job.id}`;

  return (
    <Card className={`hover:shadow-lg transition-shadow duration-200 ${className}`}>
      <CardHeader className={compact ? "pb-2" : "pb-4"}>
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <PrefetchLink
              href={jobUrl}
              prefetchStrategy="hover"
              priority={priority}
              analytics={true}
              className="block"
            >
              <CardTitle className={`hover:text-primary transition-colors ${compact ? 'text-base' : 'text-lg'}`}>
                {job.title}
              </CardTitle>
            </PrefetchLink>
            <div className="flex items-center gap-2 mt-2">
              <Badge className={statusColor}>
                {job.status.replace('_', ' ')}
              </Badge>
              <Badge className={urgencyColor}>
                {job.urgency} priority
              </Badge>
            </div>
          </div>
          {job.bidCount !== undefined && (
            <div className="text-sm text-muted-foreground">
              {job.bidCount} bid{job.bidCount !== 1 ? 's' : ''}
            </div>
          )}
        </div>
      </CardHeader>

      <CardContent className={compact ? "pt-0" : "pt-2"}>
        {/* Job Images - Lazy loaded */}
        {showImages && job.images && job.images.length > 0 && (
          <LazyWrapper
            fallback={
              <div className="w-full h-32 bg-muted rounded-md animate-pulse mb-4" />
            }
            rootMargin="100px"
            className="mb-4"
          >
            <OptimizedJobImage
              src={job.images[0]}
              alt={job.title}
              className="w-full h-32 object-cover rounded-md"
            />
          </LazyWrapper>
        )}

        {/* Job Description */}
        <p className={`text-muted-foreground mb-4 ${compact ? 'text-sm' : ''}`}>
          {truncatedDescription}
        </p>

        {/* Job Details */}
        <div className="space-y-2">
          <div className="flex items-center gap-2 text-sm">
            <DollarSignIcon className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">${job.budget.toLocaleString()}</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <MapPinIcon className="h-4 w-4" />
            <span>{job.location}</span>
          </div>
          
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <CalendarIcon className="h-4 w-4" />
            <span>{formattedDate}</span>
          </div>

          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <ClockIcon className="h-4 w-4" />
            <span>{job.trade}</span>
          </div>
        </div>

        {/* Action Button */}
        <div className="mt-4 pt-4 border-t">
          <PrefetchLink
            href={jobUrl}
            prefetchStrategy="hover"
            priority={priority}
            analytics={true}
          >
            <Button 
              variant="outline" 
              className="w-full"
              onClick={() => onCardClick?.(job.id)}
            >
              View Details
            </Button>
          </PrefetchLink>
        </div>
      </CardContent>
    </Card>
  );
};

// ============================================================================
// OPTIMIZED IMAGE COMPONENT
// ============================================================================

const OptimizedJobImage = memo(({
  src,
  alt,
  className,
}: {
  src: string;
  alt: string;
  className?: string;
}) => {
  const [imageError, setImageError] = useState(false);
  const [imageLoaded, setImageLoaded] = useState(false);

  if (imageError) {
    return (
      <div className={`bg-muted flex items-center justify-center ${className}`}>
        <span className="text-muted-foreground text-sm">Image unavailable</span>
      </div>
    );
  }

  return (
    <div className={`relative overflow-hidden ${className}`}>
      {!imageLoaded && (
        <div className="absolute inset-0 bg-muted animate-pulse" />
      )}
      <Image
        src={src}
        alt={alt}
        fill
        className={`object-cover transition-opacity duration-300 ${
          imageLoaded ? 'opacity-100' : 'opacity-0'
        }`}
        onLoad={() => setImageLoaded(true)}
        onError={() => setImageError(true)}
        sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
        priority={false}
      />
    </div>
  );
});

OptimizedJobImage.displayName = "OptimizedJobImage";

// ============================================================================
// MEMOIZATION WITH CUSTOM COMPARISON
// ============================================================================

const OptimizedJobCard = memo(OptimizedJobCardComponent, (prevProps, nextProps) => {
  // Custom comparison function for better memoization
  const jobChanged = 
    prevProps.job.id !== nextProps.job.id ||
    prevProps.job.title !== nextProps.job.title ||
    prevProps.job.status !== nextProps.job.status ||
    prevProps.job.budget !== nextProps.job.budget ||
    prevProps.job.bidCount !== nextProps.job.bidCount;

  const propsChanged = 
    prevProps.priority !== nextProps.priority ||
    prevProps.showImages !== nextProps.showImages ||
    prevProps.compact !== nextProps.compact ||
    prevProps.className !== nextProps.className;

  // Only re-render if job data or relevant props changed
  return !jobChanged && !propsChanged;
});

OptimizedJobCard.displayName = "OptimizedJobCard";

// ============================================================================
// EXPORTS
// ============================================================================

export { OptimizedJobCard };
export type { JobCardProps };
