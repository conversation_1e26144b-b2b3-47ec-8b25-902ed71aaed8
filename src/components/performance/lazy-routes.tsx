/**
 * Lazy-loaded Route Components
 * 
 * This module provides lazy-loaded versions of heavy route components
 * to improve initial bundle size and loading performance.
 */

import { Suspense } from "react";
import { createRouteComponent, createFeatureComponent } from "@/lib/performance";
import { Skeleton } from "@/components/ui/skeleton";

// ============================================================================
// LOADING COMPONENTS
// ============================================================================

export function DashboardSkeleton() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-48" />
        <Skeleton className="h-10 w-32" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        {[...Array(4)].map((_, i) => (
          <div key={i} className="space-y-2">
            <Skeleton className="h-24 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <Skeleton className="h-4 w-1/2" />
          </div>
        ))}
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Skeleton className="h-64 w-full" />
        <Skeleton className="h-64 w-full" />
      </div>
    </div>
  );
}

export function ProjectListSkeleton() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-32" />
        <Skeleton className="h-10 w-40" />
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <div key={i} className="space-y-4">
            <Skeleton className="h-48 w-full" />
            <Skeleton className="h-6 w-3/4" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-2/3" />
            <div className="flex justify-between">
              <Skeleton className="h-4 w-20" />
              <Skeleton className="h-4 w-16" />
            </div>
          </div>
        ))}
      </div>
    </div>
  );
}

export function ProjectDetailSkeleton() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <Skeleton className="h-10 w-3/4" />
      
      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        <div className="lg:col-span-2 space-y-6">
          <Skeleton className="h-64 w-full" />
          <div className="space-y-2">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
          </div>
        </div>
        
        <div className="space-y-4">
          <Skeleton className="h-32 w-full" />
          <Skeleton className="h-24 w-full" />
          <Skeleton className="h-40 w-full" />
        </div>
      </div>
    </div>
  );
}

export function CalendarSkeleton() {
  return (
    <div className="container mx-auto p-6 space-y-6">
      <div className="flex items-center justify-between">
        <Skeleton className="h-8 w-32" />
        <div className="flex gap-2">
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-20" />
          <Skeleton className="h-10 w-20" />
        </div>
      </div>
      
      <Skeleton className="h-96 w-full" />
    </div>
  );
}

// ============================================================================
// LAZY ROUTE COMPONENTS
// ============================================================================

// Dashboard Routes
export const LazyDashboard = createRouteComponent(
  () => import("@/app/(user)/dashboard/page"),
  "Dashboard"
);

export const LazyAdminDashboard = createRouteComponent(
  () => import("@/app/admin/dashboard/page"),
  "AdminDashboard"
);

// Project Routes
export const LazyProjectList = createRouteComponent(
  () => import("@/app/(user)/projects/page"),
  "ProjectList"
);

export const LazyProjectDetail = createRouteComponent(
  () => import("@/app/(user)/projects/[id]/page"),
  "ProjectDetail"
);

// Calendar Routes
export const LazyCalendar = createRouteComponent(
  () => import("@/app/(user)/calendar/page"),
  "Calendar"
);

// Profile Routes
export const LazyProfile = createRouteComponent(
  () => import("@/app/(user)/profile/page"),
  "Profile"
);

// Contractor Routes
export const LazyContractorList = createRouteComponent(
  () => import("@/app/(user)/contractors/page"),
  "ContractorList"
);

// Bid Routes
export const LazyBidList = createRouteComponent(
  () => import("@/app/(user)/bids/page"),
  "BidList"
);

// Property Routes
export const LazyPropertyList = createRouteComponent(
  () => import("@/app/(user)/properties/page"),
  "PropertyList"
);

// ============================================================================
// LAZY FEATURE COMPONENTS
// ============================================================================

// Heavy feature components that should be code-split
export const LazyFullCalendar = createFeatureComponent(
  () => import("@/components/calendar/full-calendar"),
  "FullCalendar",
  false // Don't preload
);

export const LazyImageGallery = createFeatureComponent(
  () => import("@/components/ui/image-gallery"),
  "ImageGallery",
  false
);

export const LazyDataTable = createFeatureComponent(
  () => import("@/components/ui/data-table"),
  "DataTable",
  false
);

export const LazyRichTextEditor = createFeatureComponent(
  () => import("@/components/ui/rich-text-editor"),
  "RichTextEditor",
  false
);

export const LazyFileUploader = createFeatureComponent(
  () => import("@/components/ui/file-uploader"),
  "FileUploader",
  false
);

export const LazyMapComponent = createFeatureComponent(
  () => import("@/components/ui/map"),
  "MapComponent",
  false
);

export const LazyChartComponents = createFeatureComponent(
  () => import("@/components/ui/charts"),
  "ChartComponents",
  false
);

// ============================================================================
// ROUTE WRAPPER COMPONENTS
// ============================================================================

/**
 * Wrapper component for lazy routes with proper error boundaries and loading states
 */
export function LazyRouteWrapper({
  children,
  fallback,
  routeName,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  routeName: string;
}) {
  return (
    <Suspense fallback={fallback || <DashboardSkeleton />}>
      {children}
    </Suspense>
  );
}

/**
 * Wrapper for feature components with lighter loading states
 */
export function LazyFeatureWrapper({
  children,
  fallback,
  featureName,
}: {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  featureName: string;
}) {
  return (
    <Suspense fallback={fallback || <div className="animate-pulse bg-muted h-32 rounded" />}>
      {children}
    </Suspense>
  );
}

// ============================================================================
// PRELOAD UTILITIES
// ============================================================================

/**
 * Preload critical route components
 */
export function preloadCriticalRoutes() {
  // Preload the most commonly accessed routes
  LazyDashboard.preload?.();
  LazyProjectList.preload?.();
}

/**
 * Preload routes based on user role
 */
export function preloadRoleBasedRoutes(userRole: 'homeowner' | 'contractor' | 'admin') {
  switch (userRole) {
    case 'homeowner':
      LazyProjectList.preload?.();
      LazyContractorList.preload?.();
      LazyBidList.preload?.();
      break;
    case 'contractor':
      LazyProjectList.preload?.();
      LazyCalendar.preload?.();
      LazyBidList.preload?.();
      break;
    case 'admin':
      LazyAdminDashboard.preload?.();
      break;
  }
}

/**
 * Preload features based on current route
 */
export function preloadRouteFeatures(routeName: string) {
  switch (routeName) {
    case 'calendar':
      LazyFullCalendar.preload?.();
      break;
    case 'project-detail':
      LazyImageGallery.preload?.();
      LazyMapComponent.preload?.();
      break;
    case 'dashboard':
      LazyChartComponents.preload?.();
      LazyDataTable.preload?.();
      break;
  }
}

// ============================================================================
// EXPORTS
// ============================================================================

export const lazyRoutes = {
  Dashboard: LazyDashboard,
  AdminDashboard: LazyAdminDashboard,
  ProjectList: LazyProjectList,
  ProjectDetail: LazyProjectDetail,
  Calendar: LazyCalendar,
  Profile: LazyProfile,
  ContractorList: LazyContractorList,
  BidList: LazyBidList,
  PropertyList: LazyPropertyList,
};

export const lazyFeatures = {
  FullCalendar: LazyFullCalendar,
  ImageGallery: LazyImageGallery,
  DataTable: LazyDataTable,
  RichTextEditor: LazyRichTextEditor,
  FileUploader: LazyFileUploader,
  MapComponent: LazyMapComponent,
  ChartComponents: LazyChartComponents,
};
