import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import * as React from "react";

interface JobCompletionCongratulationsProps {
  jobName: string;
  jobId: string;
  recipientRole: "HOMEOWNER" | "PROFESSIONAL";
  counterpartyName: string;
  userId: string;
  recipientEmail: string;
}

const JobCompletionCongratulationsEmail = ({
  jobName,
  jobId,
  recipientRole,
  counterpartyName,
  userId,
  recipientEmail,
}: JobCompletionCongratulationsProps) => {
  const isHomeowner = recipientRole === "HOMEOWNER";

  return (
    <Html>
      <Head />
      <Preview>Congratulations on completing your project: {jobName}</Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Project Complete!
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Congratulations on completing your project
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-green-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {jobName}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>{isHomeowner ? "Contractor" : "Client"}:</strong>{" "}
                  {counterpartyName}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "Thank you for using TradeCrews to find a professional for your project. We hope you're satisfied with the results!"
                  : "Thank you for providing your professional services through TradeCrews. We hope your client is happy with your work!"}
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}/review`}
                >
                  Leave a Review
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "Your review helps other homeowners find quality professionals. Please take a moment to share your experience."
                  : "Your feedback helps build your reputation on our platform. Please ask your client to leave a review of your work."}
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default JobCompletionCongratulationsEmail;
