import {
  <PERSON>,
  <PERSON><PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import * as React from "react";

interface NewJobNotificationProps {
  jobName: string;
  jobId: string;
  tradeName: string;
  budget: number;
  location: string;
  deadline: string;
  userId: string;
  recipientEmail: string;
}

const NewJobNotificationEmail = ({
  jobName,
  jobId,
  tradeName,
  budget,
  location,
  deadline,
  userId,
  recipientEmail,
}: NewJobNotificationProps) => {
  return (
    <Html>
      <Head />
      <Preview>
        New {tradeName} job available in your area: {jobName}
      </Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                New Job Alert
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                A new {tradeName} job has been posted in your area
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {jobName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Budget:</strong> ${budget}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Location:</strong> {location}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>Deadline:</strong> {deadline}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                This job matches your trade qualifications. Submit your bid now
                to connect with this homeowner.
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}`}
                >
                  View Job Details
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                Quick response increases your chances of winning the bid. Don't
                miss this opportunity!
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default NewJobNotificationEmail;
