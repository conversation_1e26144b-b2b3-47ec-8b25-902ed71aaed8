import {
  <PERSON>,
  But<PERSON>,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import * as React from "react";

interface ScheduledJobReminderProps {
  jobName: string;
  jobId: string;
  scheduledDate: string;
  scheduledTime: string;
  location: string;
  recipientRole: "HOMEOWNER" | "PROFESSIONAL";
  counterpartyName: string;
  userId: string;
  recipientEmail: string;
}

const ScheduledJobReminderEmail = ({
  jobName,
  jobId,
  scheduledDate,
  scheduledTime,
  location,
  recipientRole,
  counterpartyName,
  userId,
  recipientEmail,
}: ScheduledJobReminderProps) => {
  const isHomeowner = recipientRole === "HOMEOWNER";

  return (
    <Html>
      <Head />
      <Preview>
        Reminder: Your scheduled job "{jobName}" is coming up soon
      </Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Upcoming Job Reminder
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                Your scheduled job is coming up soon
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[8px] font-medium text-[18px] text-gray-700">
                  {jobName}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Date:</strong> {scheduledDate}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Time:</strong> {scheduledTime}
                </Text>
                <Text className="mb-[8px] text-[16px] text-gray-600">
                  <strong>Location:</strong> {location}
                </Text>
                <Text className="text-[16px] text-gray-600">
                  <strong>{isHomeowner ? "Contractor" : "Client"}:</strong>{" "}
                  {counterpartyName}
                </Text>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "Your contractor will arrive at the scheduled time. Please ensure the property is accessible."
                  : "Please arrive at the scheduled time. Contact the homeowner if you need access instructions."}
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href={`https://tradecrews.com/jobs/${jobId}`}
                >
                  View Job Details
                </Button>
              </Section>

              <Text className="mb-[16px] text-[16px] text-gray-600">
                {isHomeowner
                  ? "If you need to reschedule, please contact your contractor as soon as possible."
                  : "If you need to reschedule, please contact the homeowner as soon as possible."}
              </Text>
            </Section>

            <Section className="border-gray-200 border-t pt-[24px] text-center">
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={`https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`}
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default ScheduledJobReminderEmail;
