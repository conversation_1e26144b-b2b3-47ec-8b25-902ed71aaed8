import {
  <PERSON>,
  Button,
  Container,
  Head,
  Heading,
  Html,
  Preview,
  Section,
  Tailwind,
  Text,
} from "@react-email/components";
import * as React from "react";

interface TradeCrewsLaunchEmailProps {
  userId?: string;
  recipientEmail?: string;
}

const TradeCrewsLaunchEmail = ({
  userId,
  recipientEmail,
}: TradeCrewsLaunchEmailProps = {}) => {
  return (
    <Html>
      <Head />
      <Preview>
        Introducing TradeCrews - Connecting Homeowners with Skilled Tradespeople
      </Preview>
      <Tailwind>
        <Body className="bg-gray-100 py-[40px] font-sans">
          <Container className="mx-auto max-w-[600px] rounded-[8px] bg-white p-[24px]">
            <Section>
              <Heading className="mt-[10px] mb-[24px] text-center font-bold text-[28px] text-gray-800">
                Introducing <span className="text-blue-600">TradeCrews</span>
              </Heading>

              <Text className="mb-[16px] text-center font-medium text-[18px] text-gray-700">
                The smarter way to connect homeowners with skilled tradespeople
              </Text>

              <Text className="mb-[20px] text-[16px] text-gray-600">
                We're excited to announce the launch of TradeCrews, a
                revolutionary platform designed to transform how homeowners find
                and hire qualified tradespeople for their projects.
              </Text>

              <Section className="mb-[24px] rounded-[8px] bg-blue-50 p-[20px]">
                <Text className="mb-[12px] font-medium text-[16px] text-gray-700">
                  TradeCrews makes it easy to:
                </Text>
                <ul className="m-0 p-0 pl-[20px]">
                  <li className="mb-[8px] text-[16px] text-gray-600">
                    <strong>Find verified professionals</strong> with the right
                    skills and experience
                  </li>
                  <li className="mb-[8px] text-[16px] text-gray-600">
                    <strong>Compare quotes</strong> from multiple tradespeople
                  </li>
                  <li className="mb-[8px] text-[16px] text-gray-600">
                    <strong>Read authentic reviews</strong> from other
                    homeowners
                  </li>
                  <li className="text-[16px] text-gray-600">
                    <strong>Manage projects</strong> from start to finish in one
                    place
                  </li>
                </ul>
              </Section>

              <Text className="mb-[24px] text-[16px] text-gray-600">
                Whether you need a plumber, electrician, carpenter, or any other
                tradesperson, TradeCrews connects you with trusted professionals
                in your area who can get the job done right.
              </Text>

              <Section className="mb-[24px] text-center">
                <Button
                  className="box-border rounded-[4px] bg-blue-600 px-[32px] py-[14px] text-center font-bold text-[16px] text-white no-underline"
                  href="https://tradecrews.com"
                >
                  Visit TradeCrews Today
                </Button>
              </Section>

              <Section className="mb-[24px]">
                <Text className="mb-[16px] text-[16px] text-gray-600">
                  For tradespeople, TradeCrews offers a powerful platform to
                  showcase your skills, build your reputation, and connect with
                  new customers in your area.
                </Text>

                <Text className="mb-[16px] text-[16px] text-gray-600">
                  Join our growing community of professionals and start growing
                  your business today!
                </Text>
              </Section>

              <Section className="text-center">
                <Button
                  className="mb-[24px] box-border rounded-[4px] border border-blue-600 bg-white px-[24px] py-[12px] text-center font-bold text-[16px] text-blue-600 no-underline"
                  href="https://tradecrews.com/professionals"
                >
                  Register as a Professional
                </Button>
              </Section>
            </Section>

            <Section className="mt-[16px] border-gray-200 border-t pt-[20px]">
              <Text className="mb-[8px] text-center text-[14px] text-gray-600">
                Have questions? Contact us at{" "}
                <a
                  href="mailto:<EMAIL>"
                  className="text-blue-600 no-underline"
                >
                  <EMAIL>
                </a>
              </Text>

              <Text className="m-0 text-center text-[12px] text-gray-500">
                TradeCrews Inc., 123 Main Street, Suite 100, Anytown, AN 12345
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                © {new Date().getFullYear()} TradeCrews. All rights reserved.
              </Text>
              <Text className="m-0 text-center text-[12px] text-gray-500">
                <a
                  href={
                    userId && recipientEmail
                      ? `https://tradecrews.com/unsubscribe?userId=${userId}&email=${recipientEmail}`
                      : "https://tradecrews.com/unsubscribe"
                  }
                  className="text-gray-500 underline"
                >
                  Unsubscribe
                </a>
              </Text>
            </Section>
          </Container>
        </Body>
      </Tailwind>
    </Html>
  );
};

export default TradeCrewsLaunchEmail;
