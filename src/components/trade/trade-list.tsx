"use client";

import { useQuery } from "@tanstack/react-query";
import { useTRPC } from "../trpc/client";
import { TradeItem } from "./trade-item";

export function TradeList() {
  const trpc = useTRPC();
  const { data: trades } = useQuery(trpc.trades.list.queryOptions());

  return (
    <ul className="divide-y divide-white/5">
      {trades?.map((trade) => (
        <TradeItem key={trade.id} trade={trade} />
      ))}
    </ul>
  );
}
