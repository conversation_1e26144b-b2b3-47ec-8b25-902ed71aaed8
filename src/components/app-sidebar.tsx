"use client";

import { ChevronUp } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useRouter } from "next/navigation";
import logo from "@/assets/images/tc-logomark.webp";
import { useOrganization } from "@/components/contexts/organization-context";
import { signOut, useSession } from "@/lib/auth-client";
import { Avatar, AvatarFallback, AvatarImage } from "./ui/avatar";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "./ui/dropdown-menu";
import {
  Sidebar,
  SidebarContent,
  SidebarFooter,
  SidebarHeader,
  SidebarMenu,
  SidebarMenuButton,
  SidebarMenuItem,
} from "./ui/sidebar";

export function AppSidebar({
  menu,
}: Readonly<{
  menu: {
    title: string;
    href: string;
    icon: React.ElementType;
    visible: boolean;
  }[];
}>) {
  const { data: session } = useSession();
  const { organization } = useOrganization();
  const router = useRouter();
  const isAdmin = session?.user?.role === "admin";
  const isHomeowner = session?.user?.role === "homeowner";

  return (
    <Sidebar variant="inset" collapsible="offcanvas">
      <SidebarHeader>
        <Image alt="TradeCrews" src={logo} height={40} width={40} />
        {!isHomeowner && organization?.name}
      </SidebarHeader>
      <SidebarContent>
        <SidebarMenu>
          {menu.map(
            (item) =>
              item.visible && (
                <SidebarMenuItem key={item.title}>
                  <SidebarMenuButton asChild>
                    <Link href={item.href}>
                      <item.icon width={24} />
                      <span>{item.title}</span>
                    </Link>
                  </SidebarMenuButton>
                </SidebarMenuItem>
              ),
          )}
        </SidebarMenu>
      </SidebarContent>
      <SidebarFooter>
        <SidebarMenu>
          <SidebarMenuItem>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <SidebarMenuButton size={"lg"}>
                  <Avatar>
                    <AvatarImage src={session?.user?.image as string} />
                    <AvatarFallback>TC</AvatarFallback>
                  </Avatar>
                  <span>{session?.user?.name}</span>
                  <ChevronUp className="ml-auto" />
                </SidebarMenuButton>
              </DropdownMenuTrigger>
              <DropdownMenuContent
                side="right"
                className="w-[var(--radix-popper-anchor-width)]"
                align="end"
                sideOffset={4}
              >
                <DropdownMenuItem asChild>
                  <Link href="/profile">Profile</Link>
                </DropdownMenuItem>
                {!isAdmin && !isHomeowner && (
                  <DropdownMenuItem asChild>
                    <Link href={`/contractors/${organization?.id}/settings`}>
                      Contractor Settings
                    </Link>
                  </DropdownMenuItem>
                )}
                {isAdmin && (
                  <DropdownMenuItem asChild>
                    <Link href="/admin/dashboard">Admin Dashboard</Link>
                  </DropdownMenuItem>
                )}
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <button
                    type="button"
                    className="w-full"
                    onClick={() =>
                      signOut({
                        fetchOptions: {
                          onSuccess: () => {
                            router.push("/");
                          },
                        },
                      })
                    }
                  >
                    Sign Out
                  </button>
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </SidebarMenuItem>
        </SidebarMenu>
      </SidebarFooter>
    </Sidebar>
  );
}
