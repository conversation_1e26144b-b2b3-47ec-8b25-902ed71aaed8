"use client";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useMutation, useQuery } from "@tanstack/react-query";
import { QrCode } from "lucide-react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import { toast } from "sonner";
import { useTRPC } from "@/components/trpc/client";
import { Button } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card";
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Separator } from "@/components/ui/separator";
import { Switch } from "@/components/ui/switch";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { usePushNotifications } from "@/hooks/use-push-notifications";
import type { User } from "@/lib/auth";
import { type ProfileFormData, profileFormSchema } from "@/lib/schema";
import { defaultUserSettings } from "@/types/user-settings";
import { Label } from "../ui/label";

export function ProfileForm({ user }: { user: User }) {
  const router = useRouter();
  const trpc = useTRPC();
  const { isSupported, permission, isSubscribed, subscribe } =
    usePushNotifications();

  // Fetch user settings
  const { data: userSettings, isLoading } = useQuery(
    trpc.users.getSettings.queryOptions(),
  );

  const form = useForm({
    resolver: zodResolver(profileFormSchema),
    defaultValues: {
      name: user.name || "",
      settings: {
        ...defaultUserSettings,
        ...userSettings,
      },
    },
  });

  // Update profile mutation
  const updateProfile = useMutation(
    trpc.users.updateProfile.mutationOptions({
      onSuccess: () => {
        toast.success("Profile updated successfully");
      },
      onError: (error) => {
        toast.error(`Failed to update profile: ${error.message}`);
      },
    }),
  );

  // Update settings mutation
  const updateSettings = useMutation(
    trpc.users.updateSettings.mutationOptions({
      onSuccess: () => {
        toast.success("Settings updated successfully");
      },
      onError: (error) => {
        toast.error(`Failed to update settings: ${error.message}`);
      },
    }),
  );

  function onSubmit(data: ProfileFormData) {
    // Update profile information
    updateProfile.mutate({
      name: data.name,
    });

    // Update user settings
    updateSettings.mutate(data.settings);
  }

  // Handle push notification subscription
  const handlePushSubscription = async () => {
    const success = await subscribe();
    if (success) {
      // Update settings to reflect push notifications are enabled
      const currentSettings = form.getValues().settings;
      form.setValue("settings.notifications.push.enabled", true);
      updateSettings.mutate({
        ...currentSettings,
        notifications: {
          ...currentSettings.notifications,
          push: {
            ...currentSettings.notifications.push,
            enabled: true,
          },
        },
      });
    }
  };

  if (isLoading) {
    return <div>Loading...</div>;
  }

  return (
    <Tabs defaultValue="general" className="w-full">
      <TabsList>
        <TabsTrigger value="general">General</TabsTrigger>
        <TabsTrigger value="notifications">Notifications</TabsTrigger>
        <TabsTrigger value="security">Security</TabsTrigger>
      </TabsList>

      <TabsContent value="general">
        <Card>
          <CardHeader>
            <CardTitle>Profile Information</CardTitle>
            <CardDescription>Update your personal information</CardDescription>
          </CardHeader>
          <CardContent>
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <FormField
                  control={form.control}
                  name="name"
                  render={({ field }) => (
                    <FormItem>
                      <FormLabel>Name</FormLabel>
                      <FormControl>
                        <Input {...field} />
                      </FormControl>
                      <FormMessage />
                    </FormItem>
                  )}
                />

                <Button type="submit" className="mt-4">
                  Save Changes
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="notifications">
        <Card>
          <CardHeader>
            <CardTitle>Notification Settings</CardTitle>
            <CardDescription>
              Manage how you receive notifications
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <Form {...form}>
              <form
                onSubmit={form.handleSubmit(onSubmit)}
                className="space-y-6"
              >
                <div>
                  <h3 className="font-medium text-lg">Email Notifications</h3>
                  <p className="mb-4 text-muted-foreground text-sm">
                    Configure email notification preferences
                  </p>

                  <FormField
                    control={form.control}
                    name="settings.notifications.email.marketing"
                    render={({ field }) => (
                      <FormItem className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Marketing Emails
                          </FormLabel>
                          <FormDescription>
                            Receive emails about new features and opportunities
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.notifications.email.jobUpdates"
                    render={({ field }) => (
                      <FormItem className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">
                            Job Updates
                          </FormLabel>
                          <FormDescription>
                            Receive emails about job status changes
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.notifications.email.messages"
                    render={({ field }) => (
                      <FormItem className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Messages</FormLabel>
                          <FormDescription>
                            Receive emails about new messages
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />

                  <FormField
                    control={form.control}
                    name="settings.notifications.email.bids"
                    render={({ field }) => (
                      <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                        <div className="space-y-0.5">
                          <FormLabel className="text-base">Bids</FormLabel>
                          <FormDescription>
                            Receive emails about new bids on your jobs
                          </FormDescription>
                        </div>
                        <FormControl>
                          <Switch
                            checked={field.value}
                            onCheckedChange={field.onChange}
                          />
                        </FormControl>
                      </FormItem>
                    )}
                  />
                </div>

                <Separator />

                <div>
                  <h3 className="font-medium text-lg">Push Notifications</h3>
                  <p className="mb-4 text-muted-foreground text-sm">
                    Configure browser push notifications
                  </p>

                  <div className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                    <div className="space-y-0.5">
                      <FormLabel className="text-base">
                        Browser Notifications
                      </FormLabel>
                      <FormDescription>
                        Receive real-time notifications in your browser
                      </FormDescription>
                    </div>
                    {isSupported ? (
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={handlePushSubscription}
                        disabled={
                          !isSupported ||
                          permission === "denied" ||
                          isSubscribed
                        }
                      >
                        {isSubscribed
                          ? "Notifications Enabled"
                          : "Enable Notifications"}
                      </Button>
                    ) : (
                      <Button variant="outline" size="sm" disabled>
                        Not supported in your browser
                      </Button>
                    )}
                  </div>

                  {isSubscribed && (
                    <>
                      <FormField
                        control={form.control}
                        name="settings.notifications.push.jobUpdates"
                        render={({ field }) => (
                          <FormItem className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                Job Updates
                              </FormLabel>
                              <FormDescription>
                                Receive push notifications about job status
                                changes
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={!isSubscribed}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="settings.notifications.push.messages"
                        render={({ field }) => (
                          <FormItem className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">
                                Messages
                              </FormLabel>
                              <FormDescription>
                                Receive push notifications about new messages
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={!isSubscribed}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />

                      <FormField
                        control={form.control}
                        name="settings.notifications.push.bids"
                        render={({ field }) => (
                          <FormItem className="flex flex-row items-center justify-between rounded-lg border p-4">
                            <div className="space-y-0.5">
                              <FormLabel className="text-base">Bids</FormLabel>
                              <FormDescription>
                                Receive push notifications about new bids on
                                your jobs
                              </FormDescription>
                            </div>
                            <FormControl>
                              <Switch
                                checked={field.value}
                                onCheckedChange={field.onChange}
                                disabled={!isSubscribed}
                              />
                            </FormControl>
                          </FormItem>
                        )}
                      />
                    </>
                  )}
                </div>

                <Button type="submit" className="mt-4">
                  Save Changes
                </Button>
              </form>
            </Form>
          </CardContent>
        </Card>
      </TabsContent>

      <TabsContent value="security">
        <Card>
          <CardHeader>
            <CardTitle>Security Settings</CardTitle>
            <CardDescription>
              Manage your account security settings
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div>
              <h3 className="font-medium text-lg">Two-Factor Authentication</h3>
              <p className="mb-4 text-muted-foreground text-sm">
                Add an extra layer of security to your account
              </p>

              <div className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <Label className="text-base">
                    Two-Factor Authentication (2FA)
                  </Label>
                  <p className="text-muted-foreground text-sm">
                    Protect your account with an authentication app
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/profile/two-factor")}
                >
                  <QrCode className="mr-2 h-4 w-4" />
                  {user.twoFactorEnabled ? "Manage 2FA" : "Enable 2FA"}
                </Button>
              </div>

              <div className="mb-2 flex flex-row items-center justify-between rounded-lg border p-4">
                <div className="space-y-0.5">
                  <Label className="text-base">Password</Label>
                  <p className="text-muted-foreground text-sm">
                    Change your account password
                  </p>
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => router.push("/profile/change-password")}
                >
                  Change Password
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>
      </TabsContent>
    </Tabs>
  );
}
