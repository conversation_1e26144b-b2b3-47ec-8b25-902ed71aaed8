"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { CalendarIcon, ClockIcon } from "lucide-react";
import Link from "next/link";
import { QueryRenderer } from "@/components/query-renderer";
import { ScheduleCalendar } from "@/components/schedule/schedule-calendar";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";

interface CalendarContentProps {
  organizationId: string;
}

export function CalendarContent({ organizationId }: CalendarContentProps) {
  const trpc = useTRPC();

  const { data: scheduledJobs, isLoading } = useQuery(
    trpc.jobs.listScheduledForOrganization.queryOptions({
      organizationId,
    })
  );

  return (
    <div className="p-6">
      <div className="grid grid-cols-1 gap-6 lg:grid-cols-3">
        <div className="lg:col-span-2">
          <Card>
            <CardHeader>
              <CardTitle>Schedule Calendar</CardTitle>
            </CardHeader>
            <CardContent>
              <QueryRenderer
                data={scheduledJobs}
                isLoading={isLoading}
                loadingComponent={<div>Loading calendar...</div>}
                emptyComponent={
                  <div className="py-10 text-center">
                    <p className="text-muted-foreground">
                      No scheduled jobs found.
                    </p>
                  </div>
                }
              >
                {(jobs) => <ScheduleCalendar jobs={jobs} />}
              </QueryRenderer>
            </CardContent>
          </Card>
        </div>

        <div>
          <Card>
            <CardHeader>
              <CardTitle>Upcoming Jobs</CardTitle>
            </CardHeader>
            <CardContent>
              <QueryRenderer
                data={scheduledJobs}
                isLoading={isLoading}
                loadingComponent={<div>Loading scheduled jobs...</div>}
                emptyComponent={
                  <div className="py-10 text-center">
                    <p className="text-muted-foreground">
                      No scheduled jobs found.
                    </p>
                  </div>
                }
              >
                {(jobs) => (
                  <div className="space-y-4">
                    {jobs.map((job) => (
                      <div key={job.id} className="rounded-md border p-4">
                        <div className="flex items-center justify-between">
                          <div>
                            <h3 className="font-medium">{job.name}</h3>
                            {job.jobType === "QUICK_HIRE" && (
                              <Badge variant="secondary" className="mt-1">
                                Quick Hire
                              </Badge>
                            )}
                          </div>
                          <Button variant="outline" size="sm" asChild>
                            <Link href={`/jobs/${job.id}`}>View</Link>
                          </Button>
                        </div>

                        <div className="mt-2 space-y-1 text-muted-foreground text-sm">
                          <div className="flex items-center gap-2">
                            <CalendarIcon className="h-3.5 w-3.5" />
                            <span>
                              {job.schedules[0]?.confirmedStartDate &&
                              job.schedules[0]?.confirmedEndDate
                                ? `${format(
                                    job.schedules[0].confirmedStartDate,
                                    "MMM d, yyyy",
                                  )} - ${format(
                                    job.schedules[0].confirmedEndDate,
                                    "MMM d, yyyy",
                                  )}`
                                : "Schedule pending"}
                            </span>
                          </div>
                          <div className="flex items-center gap-2">
                            <ClockIcon className="h-3.5 w-3.5" />
                            <span>
                              Status: {job.schedules[0]?.status || "Pending"}
                            </span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </QueryRenderer>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  );
}
