"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import { Building2, ClockIcon, MapPin } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import { useTRPC } from "@/components/trpc/client";
import { Badge } from "@/components/ui/badge";
import { Button, buttonVariants } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { Skeleton } from "@/components/ui/skeleton";

export function PropertyDetailContent({ id }: { id: string }) {
  const trpc = useTRPC();

  const { data: property } = useQuery(trpc.properties.one.queryOptions({ id }));
  const { data: propertyJobs, isLoading: isLoadingJobs } = useQuery(
    trpc.jobs.listByProperty.queryOptions({ propertyId: id })
  );

  if (!property) {
    return (
      <div className="text-center">
        <p>The requested property could not be found.</p>
        <Link
          href="/properties"
          className={buttonVariants({
            variant: "default",
            className: "mt-4",
          })}
        >
          Back to Properties
        </Link>
      </div>
    );
  }

  // Group jobs by year
  const jobsByYear = propertyJobs?.reduce((acc, job) => {
    if (job.completedAt) {
      const year = format(new Date(job.completedAt), "yyyy");
      if (!acc[year]) {
        acc[year] = [];
      }
      acc[year].push(job);
    }
    return acc;
  }, {} as Record<string, typeof propertyJobs>);

  // Sort years in descending order
  const sortedYears = Object.keys(jobsByYear || {}).sort(
    (a, b) => Number.parseInt(b) - Number.parseInt(a)
  );

  return (
    <div className="container mx-auto p-6 md:p-8">
      <div className="mb-8 grid grid-cols-1 gap-8 md:grid-cols-3">
        {/* Property Image */}
        <div className="relative h-64 overflow-hidden rounded-lg md:h-full">
          <Image
            src={property.imageUrl || ""}
            alt={property.name}
            fill
            className="object-cover"
            priority
          />
        </div>

        {/* Property Details */}
        <Card className="md:col-span-2">
          <CardHeader>
            <CardTitle>Property Details</CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <h3 className="font-medium">Address</h3>
              {property.address && (
                <div className="mt-1 flex items-start gap-1.5 text-muted-foreground">
                  <MapPin className="mt-0.5 h-4 w-4 flex-shrink-0" />
                  <span>
                    {property.address.street}, {property.address.city},{" "}
                    {property.address.state} {property.address.zip}
                  </span>
                </div>
              )}
            </div>

            <div>
              <h3 className="font-medium">Property Type</h3>
              <div className="mt-1 flex items-center gap-1.5">
                <Building2 className="h-4 w-4 text-orange-600" />
                <Badge variant="outline" className="bg-orange-50 text-orange-700">
                  Property
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Property History Timeline */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ClockIcon className="h-5 w-5 text-orange-500" />
            Property History
          </CardTitle>
        </CardHeader>
        <CardContent>
          {isLoadingJobs ? (
            <div className="space-y-4">
              <Skeleton className="h-24 w-full" />
              <Skeleton className="h-24 w-full" />
            </div>
          ) : sortedYears.length === 0 ? (
            <div className="py-8 text-center text-muted-foreground">
              <p>No completed projects found for this property.</p>
              <Button
                className="mt-4"
                onClick={() =>
                  document.getElementById("new-property-trigger")?.click()
                }
              >
                Start a New Project
              </Button>
            </div>
          ) : (
            <div className="space-y-8">
              {sortedYears.map((year) => (
                <div key={year}>
                  <h3 className="mb-4 font-medium text-lg">{year}</h3>
                  <Separator className="mb-4" />
                  <div className="space-y-4">
                    {(jobsByYear?.[year] || [])?.map((job) => (
                      <Link href={`/jobs/${job.id}`} key={job.id} className="block">
                        <div className="rounded-lg border p-4 transition-colors hover:bg-muted/50">
                          <div className="flex items-start justify-between">
                            <div>
                              <h4 className="font-medium">{job.name}</h4>
                              <p className="text-muted-foreground text-sm">
                                Completed:{" "}
                                {format(job.completedAt as Date, "MMMM d, yyyy")}
                              </p>
                            </div>
                            <Badge>{job.status}</Badge>
                          </div>
                        </div>
                      </Link>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
