import { Building2, <PERSON>, Eye, MapPin, Plus } from "lucide-react";
import { useState } from "react";
import { EntityCard } from "@/components/common/entity-card";
import { JobWizard } from "@/components/job/job-wizard";
import type { Property } from "@/db/schema";

export function PropertyCard({ property }: Readonly<{ property: Property }>) {
  const [wizardOpen, setWizardOpen] = useState(false);

  const metadata = [
    {
      icon: <MapPin className="h-4 w-4" />,
      label: "Address",
      value: property.address
        ? `${property.address.street}, ${property.address.city}, ${property.address.state} ${property.address.zip}`
        : "No address provided",
    },
    {
      icon: <Building2 className="h-4 w-4" />,
      label: "Type",
      value: "Property",
    },
  ];

  const actions = [
    {
      label: "View Details",
      href: `/properties/${property.id}`,
      variant: "tc_blue" as const,
      icon: <Eye className="h-4 w-4" />,
    },
    {
      label: "Edit",
      href: `/properties/${property.id}/edit`,
      variant: "outline" as const,
      icon: <Edit className="h-4 w-4" />,
    },
    {
      label: "New Project",
      onClick: () => setWizardOpen(true),
      variant: "tc_orange" as const,
      icon: <Plus className="h-4 w-4" />,
    },
  ];

  return (
    <>
      <EntityCard
        title={property.name}
        imageUrl={property.imageUrl || ""}
        imageAlt={property.name}
        metadata={metadata}
        actions={actions}
        variant="default"
        className="max-w-80"
      />
      <JobWizard
        open={wizardOpen}
        onOpenChange={setWizardOpen}
        propertyId={property.id}
      />
    </>
  );
}
