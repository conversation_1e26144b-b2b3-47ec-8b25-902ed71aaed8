"use client";

import { useQuery } from "@tanstack/react-query";
import { PropertyCard } from "@/components/properties/property-card";
import { useTRPC } from "@/components/trpc/client";

export function PropertiesContent() {
  const trpc = useTRPC();
  const { data: properties } = useQuery(trpc.properties.list.queryOptions());

  if (!properties || properties.length === 0) {
    return (
      <div className="text-center">
        <p className="text-muted-foreground">
          No properties found. Create your first property to get started.
        </p>
      </div>
    );
  }

  return (
    <>
      {properties.map((property) => (
        <PropertyCard key={property.id} property={property} />
      ))}
    </>
  );
}
