import { Header } from "@/components/header";

interface PageLayoutProps {
  title?: string;
  children: React.ReactNode;
  containerClassName?: string;
  actions?: React.ReactNode;
}

export function PageLayout({
  title,
  children,
  containerClassName = "mx-auto p-6 md:p-8",
  actions,
}: PageLayoutProps) {
  return (
    <>
      {title && <Header title={title} actions={actions} />}
      <div className={`container ${containerClassName}`}>{children}</div>
    </>
  );
}
