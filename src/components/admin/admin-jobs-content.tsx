"use client";

import { useQuery } from "@tanstack/react-query";
import { format } from "date-fns";
import Link from "next/link";
import { useTRPC } from "@/components/trpc/client";
import { buttonVariants } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";

export function AdminJobsContent() {
  const trpc = useTRPC();
  const { data: jobs } = useQuery(trpc.jobs.list.queryOptions());

  return (
    <div className="space-y-4">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead>Name</TableHead>
            <TableHead>Property</TableHead>
            <TableHead>Budget</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Bids</TableHead>
            <TableHead>Created At</TableHead>
            <TableHead className="text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {jobs?.map((job) => (
            <TableRow key={job.id} className="border-b">
              <TableCell className="font-medium">
                <Link href={`/admin/jobs/${job.id}`} className="hover:text-orange-600">
                  {job.name}
                </Link>
              </TableCell>
              <TableCell>{job.property?.name || "N/A"}</TableCell>
              <TableCell>${job.budget}</TableCell>
              <TableCell>{job.status}</TableCell>
              <TableCell>{job.bidsCount}</TableCell>
              <TableCell>{format(job.createdAt, "MMM d, yyyy")}</TableCell>
              <TableCell className="text-right">
                <Link
                  href={`/admin/jobs/${job.id}`}
                  className={buttonVariants({
                    variant: "outline",
                    size: "sm",
                    className: "mr-2",
                  })}
                >
                  View
                </Link>
                <Link
                  href={`/admin/jobs/${job.id}/edit`}
                  className={buttonVariants({
                    variant: "outline",
                    size: "sm",
                  })}
                >
                  Edit
                </Link>
              </TableCell>
            </TableRow>
          ))}
        </TableBody>
      </Table>
    </div>
  );
}
