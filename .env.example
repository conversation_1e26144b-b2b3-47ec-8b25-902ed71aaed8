# Since the ".env" file is gitignored, you can use the ".env.example" file to
# build a new ".env" file when you clone the repo. Keep this file up-to-date
# when you add new variables to `.env`.

# This file will be committed to version control, so make sure not to have any
# secrets in it. If you are cloning this repo, create a copy of this file named
# ".env" and populate it with your secrets.

# When adding additional environment variables, the schema in "/src/env.js"
# should be updated accordingly.

# Database
DATABASE_URL="postgresql://username:password@localhost:5432/database"
DIRECT_URL="postgresql://username:password@localhost:5432/database"

# Better Auth
BETTER_AUTH_SECRET="your_auth_secret"
BETTER_AUTH_URL="https://your-auth-url.com"

# Google OAuth
GOOGLE_CLIENT_ID="your_google_client_id"
GOOGLE_CLIENT_SECRET="your_google_client_secret"

# Microsoft OAuth
MICROSOFT_CLIENT_ID="your_microsoft_client_id"
MICROSOFT_CLIENT_SECRET="your_microsoft_client_secret"

# File Storage
TRANSLOADIT_KEY="your_transloadit_key"
TRANSLOADIT_SECRET="your_transloadit_secret"
TRANSLOADIT_TEMPLATE_ID="your_template_id"
NEXT_PUBLIC_STORAGE_URL="https://your-storage-url.com"

# Pusher (required for chat functionality)
PUSHER_APP_ID="your_pusher_app_id"
PUSHER_KEY="your_pusher_key"
PUSHER_SECRET="your_pusher_secret"
PUSHER_CLUSTER="us2"
NEXT_PUBLIC_PUSHER_KEY="your_pusher_key"
NEXT_PUBLIC_PUSHER_CLUSTER="us2"

# Geocoding
GEOCODING_API_URL="https://nominatim.openstreetmap.org"

# OpenAI
OPENAI_API_KEY="sk-your_openai_key"

# Web Push Notifications
NEXT_PUBLIC_VAPID_PUBLIC_KEY="your_vapid_public_key"
VAPID_PRIVATE_KEY="your_vapid_private_key"

# Email (Resend)
RESEND_API_KEY="re_your_resend_key"

# PostHog Analytics
NEXT_PUBLIC_POSTHOG_KEY="phc_your_posthog_key"
NEXT_PUBLIC_POSTHOG_HOST="https://us.i.posthog.com"
