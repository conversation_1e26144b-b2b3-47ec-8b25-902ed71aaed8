# tRPC Query Optimization Guide

This guide covers the comprehensive query optimization utilities implemented for your tRPC setup, building upon the schema standardization from the previous optimization phase.

## Overview

The query optimization system provides:

1. **Query Configuration Utilities** - Different cache strategies for different data types
2. **Advanced Caching Strategies** - Background refresh, stale-while-revalidate patterns
3. **Performance Optimization Helpers** - Infinite queries, parallel execution, batching
4. **Server-Side Query Enhancements** - Enhanced prefetching, deduplication
5. **Query State Management** - Loading state aggregation, error handling

## Core Components

### 1. Query Cache Strategies (`src/lib/trpc/query-client.ts`)

Different data types require different caching strategies:

```typescript
import { QUERY_CACHE_STRATEGIES } from "@/lib/trpc/query-client";

// Static data (trades, templates) - cache for 5 minutes
QUERY_CACHE_STRATEGIES.STATIC

// User-specific data (profile, organization) - cache for 2 minutes  
QUERY_CACHE_STRATEGIES.USER

// Dynamic data (jobs, bids) - cache for 30 seconds
QUERY_CACHE_STRATEGIES.DYNAMIC

// Real-time data (messages, notifications) - always fresh
QUERY_CACHE_STRATEGIES.REALTIME

// Background data (analytics) - cache for 10 minutes
QUERY_CACHE_STRATEGIES.BACKGROUND
```

### 2. Query Key Factory (`src/lib/trpc/utils/query-optimization.ts`)

Consistent query key management:

```typescript
import { queryKeys } from "@/lib/trpc/utils/query-optimization";

// Entity-specific keys
queryKeys.job(jobId)
queryKeys.jobBids(jobId)
queryKeys.jobsByProperty(propertyId)
queryKeys.organization(orgId)
queryKeys.messagesByBid(bidId)
```

### 3. Query Option Builders

Apply different cache strategies easily:

```typescript
import { queryOptionBuilders } from "@/lib/trpc/utils/query-optimization";

// Static data with long cache
const tradesQuery = queryOptionBuilders.static(
  trpc.trades.list.queryOptions()
);

// Dynamic data with short cache
const jobsQuery = queryOptionBuilders.dynamic(
  trpc.jobs.list.queryOptions()
);

// Conditional query that only runs when enabled
const orgQuery = queryOptionBuilders.conditional(
  trpc.contractor.getForUser.queryOptions(),
  userRole === "contractor"
);
```

## Usage Patterns

### Client-Side Query Optimization

#### 1. Using Optimized Query Hooks

```typescript
import { useOptimizedQueries } from "@/lib/trpc/utils/query-hooks";

function MyComponent() {
  const { 
    aggregateLoadingStates,
    aggregateErrorStates,
    invalidateJobRelated 
  } = useOptimizedQueries();

  const job = trpc.jobs.getById.useQuery({ id: jobId });
  const bids = trpc.bids.listByJob.useQuery({ jobId });
  
  const isLoading = aggregateLoadingStates(job, bids);
  const error = aggregateErrorStates(job, bids);

  const handleJobUpdate = () => {
    // Invalidate all job-related queries
    invalidateJobRelated(jobId, propertyId);
  };
}
```

#### 2. Performance Monitoring

```typescript
import { useQueryPerformance } from "@/lib/trpc/utils/query-hooks";

function App() {
  const { slowQueries, trackQuery } = useQueryPerformance();

  // Track slow queries automatically
  React.useEffect(() => {
    console.log('Slow queries:', slowQueries);
  }, [slowQueries]);
}
```

### Server-Side Prefetching

#### 1. Enhanced Prefetching

```typescript
import { prefetchParallel, prefetchConditional, prefetchSmart } from "@/components/trpc/server";

// In your page component
export default async function JobPage({ params }) {
  const { id } = await params;

  // Prefetch multiple queries in parallel
  await prefetchParallel([
    trpc.jobs.getById.queryOptions({ id }),
    trpc.bids.listByJob.queryOptions({ jobId: id }),
    trpc.messages.listMessages.queryOptions({ jobId: id }),
  ]);

  // Conditional prefetching based on user role
  await prefetchConditional([
    {
      condition: userRole === "contractor",
      queryOptions: trpc.contractor.getForUser.queryOptions(),
    },
    {
      condition: userRole === "homeowner", 
      queryOptions: trpc.properties.list.queryOptions(),
    },
  ]);

  // Smart prefetching (only if data is stale)
  await prefetchSmart(
    trpc.users.profile.queryOptions({ id: userId }),
    60000 // Only prefetch if older than 1 minute
  );

  return <JobDetails id={id} />;
}
```

### Infinite Queries

#### 1. Cursor-Based Pagination

```typescript
import { infiniteQueryBuilders } from "@/lib/trpc/utils/infinite-query";

// In your router
export const jobsRouter = {
  listInfinite: protectedProcedure
    .input(cursorPaginationSchema.extend({
      status: z.string().optional(),
    }))
    .query(async ({ input }) => {
      const jobs = await db.query.job.findMany({
        limit: input.limit + 1,
        where: input.cursor ? gt(job.createdAt, input.cursor) : undefined,
        orderBy: [desc(job.createdAt)],
      });

      const hasNextPage = jobs.length > input.limit;
      const items = hasNextPage ? jobs.slice(0, -1) : jobs;
      const nextCursor = hasNextPage ? items[items.length - 1].createdAt : undefined;

      return {
        items,
        nextCursor,
        hasNextPage,
      };
    }),
};

// In your component
function JobsList() {
  const {
    data,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = trpc.jobs.listInfinite.useInfiniteQuery(
    { limit: 20 },
    infiniteQueryBuilders.cursor({
      getNextPageParam: (lastPage) => lastPage.nextCursor,
      initialPageParam: undefined,
    })
  );

  const jobs = data?.pages.flatMap(page => page.items) ?? [];
}
```

### Parallel Query Execution

```typescript
import { parallelQueries } from "@/lib/trpc/utils/parallel-queries";

// Load dashboard data in parallel
const loadDashboardData = async () => {
  const result = await parallelQueries.executeParallel(queryClient, {
    user: () => queryClient.fetchQuery(trpc.users.profile.queryOptions({ id: userId })),
    jobs: () => queryClient.fetchQuery(trpc.jobs.list.queryOptions({ limit: 10 })),
    properties: () => queryClient.fetchQuery(trpc.properties.list.queryOptions()),
  });

  if (result.data) {
    // All queries succeeded
    const { user, jobs, properties } = result.data;
  } else {
    // Handle errors
    console.error('Errors:', result.errors);
  }
};
```

### Cache Invalidation

#### 1. Smart Invalidation

```typescript
import { cacheInvalidation } from "@/lib/trpc/utils/query-optimization";

// When a job is updated
const handleJobUpdate = async (jobId: string, propertyId?: string) => {
  await cacheInvalidation.invalidateJobRelated(queryClient, jobId, propertyId);
};

// When a bid is created/updated  
const handleBidUpdate = async (bidId: string, jobId?: string) => {
  await cacheInvalidation.invalidateBidRelated(queryClient, bidId, jobId);
};

// When messages are sent
const handleMessageSent = async ({ bidId, jobId }: { bidId?: string; jobId?: string }) => {
  await cacheInvalidation.invalidateMessageRelated(queryClient, { bidId, jobId });
};
```

## Best Practices

### 1. Choose the Right Cache Strategy

- **Static data** (trades, templates): Use `QUERY_CACHE_STRATEGIES.STATIC`
- **User data** (profile, organization): Use `QUERY_CACHE_STRATEGIES.USER`  
- **Dynamic data** (jobs, bids): Use `QUERY_CACHE_STRATEGIES.DYNAMIC`
- **Real-time data** (messages): Use `QUERY_CACHE_STRATEGIES.REALTIME`

### 2. Prefetch Related Data

Always prefetch related data on server-side for better UX:

```typescript
// Good: Prefetch related data
await prefetchParallel([
  trpc.jobs.getById.queryOptions({ id }),
  trpc.bids.listByJob.queryOptions({ jobId: id }),
  trpc.property.one.queryOptions({ id: propertyId }),
]);

// Bad: Load data sequentially on client
const job = trpc.jobs.getById.useQuery({ id });
const bids = trpc.bids.listByJob.useQuery({ jobId: id }, { enabled: !!job.data });
```

### 3. Use Consistent Query Keys

Always use the query key factory for consistent cache management:

```typescript
// Good: Use query key factory
queryClient.invalidateQueries({ queryKey: queryKeys.job(jobId) });

// Bad: Manual query keys (inconsistent)
queryClient.invalidateQueries({ queryKey: ['jobs', jobId] });
```

### 4. Aggregate Loading States

For components that depend on multiple queries:

```typescript
// Good: Aggregate states
const isLoading = aggregateLoadingStates(job, bids, messages);
const error = aggregateErrorStates(job, bids, messages);

// Bad: Manual state management
const isLoading = job.isLoading || bids.isLoading || messages.isLoading;
```

### 5. Monitor Performance

Use the performance monitoring hooks to identify slow queries:

```typescript
const { slowQueries, trackQuery } = useQueryPerformance();

React.useEffect(() => {
  if (slowQueries.length > 0) {
    console.warn('Slow queries detected:', slowQueries);
  }
}, [slowQueries]);
```

## Migration Guide

### From Basic Queries

**Before:**
```typescript
const jobs = trpc.jobs.list.useQuery();
const bids = trpc.bids.listByJob.useQuery({ jobId });
```

**After:**
```typescript
const jobs = trpc.jobs.list.useQuery(
  {},
  queryOptionBuilders.dynamic(trpc.jobs.list.queryOptions())
);
const bids = trpc.bids.listByJob.useQuery(
  { jobId },
  queryOptionBuilders.dynamic(trpc.bids.listByJob.queryOptions({ jobId }))
);
```

### From Manual Prefetching

**Before:**
```typescript
await queryClient.prefetchQuery(trpc.jobs.getById.queryOptions({ id }));
await queryClient.prefetchQuery(trpc.bids.listByJob.queryOptions({ jobId: id }));
```

**After:**
```typescript
await prefetchParallel([
  trpc.jobs.getById.queryOptions({ id }),
  trpc.bids.listByJob.queryOptions({ jobId: id }),
]);
```

## Performance Benefits

1. **Reduced Server Load**: Smart caching reduces unnecessary API calls
2. **Better UX**: Parallel prefetching eliminates loading waterfalls  
3. **Consistent Performance**: Standardized cache strategies across the app
4. **Easier Debugging**: Performance monitoring identifies bottlenecks
5. **Maintainable Code**: Centralized query management and consistent patterns

## Next Steps

With query optimization in place, you're ready for the next phase:
- Enhanced permission system
- Standardized error handling  
- Generic CRUD builders

The query optimization utilities provide a solid foundation for building performant, scalable tRPC applications.
