# Common Components Documentation

This document describes the reusable common components created to standardize UI patterns across the application.

## Overview

The common components library provides:
- **EntityCard**: Generic card component for displaying entities (properties, jobs, bids, etc.)
- **FormWrapper**: Standardized form layout and submission handling
- **Form Fields**: Type-safe form field components with consistent styling
- **DataRenderer**: Enhanced data fetching and state management
- **Page Layouts**: Standardized page layout patterns

## EntityCard

A flexible card component that can display any type of entity with consistent styling and behavior.

### Basic Usage

```tsx
import { EntityCard } from "@/components/common";

<EntityCard
  title="Property Name"
  subtitle="Property Type"
  description="Property description..."
  imageUrl="/property-image.jpg"
  metadata={[
    {
      icon: <MapPin className="h-4 w-4" />,
      label: "Location",
      value: "123 Main St, City, State"
    }
  ]}
  actions={[
    {
      label: "View Details",
      href: "/properties/123",
      variant: "tc_blue"
    }
  ]}
/>
```

### Props

- `title`: Main title text
- `subtitle`: Optional subtitle
- `description`: Optional description text
- `imageUrl`: Optional image URL
- `metadata`: Array of metadata items with icons
- `actions`: Array of action buttons
- `variant`: "default" | "compact" | "detailed"
- `orientation`: "vertical" | "horizontal"

## FormWrapper

Standardized form wrapper with consistent layout, validation, and submission handling.

### Basic Usage

```tsx
import { FormWrapper } from "@/components/common";
import { useForm } from "react-hook-form";

const form = useForm<FormData>({
  resolver: zodResolver(schema),
  defaultValues: {...}
});

<FormWrapper
  form={form}
  onSubmit={handleSubmit}
  title="Create Property"
  description="Add a new property to your account"
  submitLabel="Create Property"
  cancelHref="/properties"
  variant="card"
>
  {/* Form fields */}
</FormWrapper>
```

### Features

- Automatic form state management
- Consistent button layouts
- Loading states
- Error handling
- Sectioned forms
- Multiple layout variants

## Form Fields

Type-safe form field components with consistent styling and validation.

### Available Fields

```tsx
import { 
  TextField, 
  NumberField, 
  TextareaField, 
  SelectField,
  DateField,
  SwitchField 
} from "@/components/common";

// Text input
<TextField
  form={form}
  name="name"
  label="Property Name"
  placeholder="Enter property name"
  required
/>

// Number input
<NumberField
  form={form}
  name="budget"
  label="Budget"
  min={0}
  step={100}
/>

// Select dropdown
<SelectField
  form={form}
  name="type"
  label="Property Type"
  options={[
    { value: "house", label: "House" },
    { value: "apartment", label: "Apartment" }
  ]}
/>

// Date picker
<DateField
  form={form}
  name="startDate"
  label="Start Date"
  disablePast
/>
```

## DataRenderer

Enhanced data fetching component with loading states, error handling, and empty states.

### Basic Usage

```tsx
import { DataRenderer, createDataState } from "@/components/common";

const queryResult = useQuery(trpc.properties.list.queryOptions());
const dataState = createDataState(queryResult);

<DataRenderer
  state={dataState}
  loadingTitle="Loading Properties"
  emptyTitle="No Properties Found"
  emptyDescription="Create your first property to get started"
  emptyAction={{
    label: "Create Property",
    onClick: () => router.push("/properties/new")
  }}
>
  {(properties) => (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      {properties.map(property => (
        <PropertyCard key={property.id} property={property} />
      ))}
    </div>
  )}
</DataRenderer>
```

### Features

- Loading skeletons
- Error states with retry
- Empty states with actions
- Refresh indicators
- Card and inline variants

## Page Layouts

Standardized page layout components for consistent page structure.

### DetailPageLayout

For entity detail pages with optional sidebar.

```tsx
import { DetailPageLayout } from "@/components/common";

<DetailPageLayout
  header={{
    title: "Property Details",
    description: "View and manage property information",
    breadcrumbs: [
      { label: "Properties", href: "/properties" },
      { label: property.name }
    ],
    actions: [
      {
        label: "Edit Property",
        href: `/properties/${property.id}/edit`,
        variant: "tc_blue"
      }
    ]
  }}
  sidebar={<PropertySidebar property={property} />}
>
  <PropertyDetails property={property} />
</DetailPageLayout>
```

### ListPageLayout

For list/grid pages with optional filters.

```tsx
<ListPageLayout
  header={{
    title: "Properties",
    description: "Manage your properties",
    primaryAction: {
      label: "Add Property",
      href: "/properties/new",
      variant: "tc_blue",
      icon: <Plus className="h-4 w-4" />
    }
  }}
  filters={<PropertyFilters />}
>
  <PropertyGrid properties={properties} />
</ListPageLayout>
```

### FormPageLayout

For form pages with consistent layout.

```tsx
<FormPageLayout
  header={{
    title: "Create Property",
    backButton: { href: "/properties", label: "Back to Properties" }
  }}
  variant="card"
  maxWidth="lg"
>
  <PropertyForm />
</FormPageLayout>
```

## Migration Guide

### Converting Existing Cards

**Before:**
```tsx
<Card className="...">
  <CardHeader>
    <CardTitle>{title}</CardTitle>
  </CardHeader>
  <CardContent>
    {/* Custom content */}
  </CardContent>
  <CardFooter>
    {/* Custom actions */}
  </CardFooter>
</Card>
```

**After:**
```tsx
<EntityCard
  title={title}
  metadata={metadata}
  actions={actions}
  variant="default"
/>
```

### Converting Forms

**Before:**
```tsx
<Form {...form}>
  <form onSubmit={form.handleSubmit(onSubmit)}>
    {/* Form fields */}
    <Button type="submit">Submit</Button>
  </form>
</Form>
```

**After:**
```tsx
<FormWrapper
  form={form}
  onSubmit={onSubmit}
  submitLabel="Submit"
>
  {/* Form fields using common field components */}
</FormWrapper>
```

## Best Practices

1. **Use EntityCard for all card-like components** - Provides consistent styling and behavior
2. **Use FormWrapper for all forms** - Handles common form patterns and validation
3. **Use common form fields** - Ensures consistent styling and validation
4. **Use DataRenderer for data fetching** - Provides consistent loading and error states
5. **Use page layouts for consistent structure** - Standardizes page layouts across the app

## Benefits

- **Consistency**: All components follow the same design patterns
- **Maintainability**: Changes to common patterns only need to be made in one place
- **Type Safety**: All components are fully typed with TypeScript
- **Accessibility**: Common components include proper accessibility features
- **Performance**: Optimized components with proper memoization
- **Developer Experience**: Easy to use with comprehensive TypeScript support
